from enum import Enum
import uuid
import time
from typing import List, Optional

from sqlmodel import Field, text

from app.models.base_model import BaseModel

class UserBase(BaseModel, table=False):
    username: str = Field(nullable=False, unique=True, max_length=50)
    email: str = Field(nullable=False, unique=True, max_length=255)

# User schemas
class User(UserBase, table=True):
    __tablename__ = "users"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    # parse as YYYY-MM-DDTHH:MM:SSZ
    created_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={"server_default": text("CURRENT_TIMESTAMP")}
    )

    updated_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={
            "server_default": text("CURRENT_TIMESTAMP"),
            "onupdate": text("CURRENT_TIMESTAMP")
        }
    )

class UserCreate(UserBase):
    pass

class UserRead(UserBase):
    pass

class UserUpdate(UserBase):
    pass
