from enum import Enum
import time
import uuid
from typing import List, Optional

from sqlalchemy import Column
from sqlmodel import Field, text
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER

from app.models.base_model import BaseModel

class SubsidiaryBase(BaseModel, table=False):
    tenant_id: str = Field(nullable=False)
    email: str = Field(nullable=False)
    contact_name: str = Field(nullable=False)
    entity_name: str = Field(nullable=False)
    entity_code: str = Field(nullable=False)

# Subsidiary schemas
class Subsidiary(SubsidiaryBase, table=True):
    __tablename__ = "subsidiaries"

    id: Optional[uuid.UUID] = Field(
        default_factory=uuid.uuid4,
        primary_key=True
    )
    # parse as YYYY-MM-DDTHH:MM:SSZ
    created_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={"server_default": text("CURRENT_TIMESTAMP")}
    )

    updated_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={
            "server_default": text("CURRENT_TIMESTAMP"),
            "onupdate": text("CURRENT_TIMESTAMP")
        }
    )

class SubsidiaryCreate(SubsidiaryBase):
    pass

class SubsidiaryUpdate(SubsidiaryBase):
    pass

class SubsidiaryRead(SubsidiaryBase):
    pass