from enum import Enum
import uuid
import time
from typing import List, Optional

from sqlmodel import SQLModel, Field, Column, text
from sqlalchemy import Column, VARCHAR

from app.models.base_model import BaseModel

class RequestStatus(str, Enum):
    DATA_INCOMPLETE = "DATA_INCOMPLETE"
    READY_TO_PROCESS = "READY_TO_PROCESS"
    PROCESSING = "PROCESSING"
    DONE = "DONE"

class RequestBase(BaseModel, SQLModel, table=False):
    user_id: str = Field(nullable=False)
    status: RequestStatus = Field(
        sa_column=Column("status", VARCHAR(32)),
        default=RequestStatus.DATA_INCOMPLETE
    )

# Request schemas
class Request(RequestBase, table=True):
    __tablename__ = "requests"
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    # parse as YYYY-MM-DDTHH:MM:SSZ
    created_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={"server_default": text("CURRENT_TIMESTAMP")}
    )

    updated_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={
            "server_default": text("CURRENT_TIMESTAMP"),
            "onupdate": text("CURRENT_TIMESTAMP")
        }
    )

class RequestCreate(RequestBase):
    pass

class RequestRead(RequestBase):
    pass

class RequestUpdate(BaseModel):
    pass