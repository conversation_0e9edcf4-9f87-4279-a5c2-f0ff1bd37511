from enum import Enum
import time
import uuid
from typing import List, Optional

from sqlmodel import Field, text

from app.models.base_model import BaseModel

class FileBase(BaseModel, table=False):
    request_id: str = Field(nullable=False)
    file_name: str = Field(nullable=False)
    file_path: str = Field(nullable=False)
    blob_url: str = Field(nullable=False)

# File schemas
class File(FileBase, table=True):
    __tablename__ = "files"

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()), primary_key=True)
    # parse as YYYY-MM-DDTHH:MM:SSZ
    created_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={"server_default": text("CURRENT_TIMESTAMP")}
    )

    updated_at: Optional[str] = Field(
        default_factory=lambda: time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        sa_column_kwargs={
            "server_default": text("CURRENT_TIMESTAMP"),
            "onupdate": text("CURRENT_TIMESTAMP")
        }
    )

class FileCreate(FileBase):
    pass

class FileRead(FileBase):
    pass

class FileUpdate(FileBase):
    pass