"""Utils."""

def iou(box1, box2):
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    intersection = max(0, x2 - x1 + 1) * max(0, y2 - y1 + 1)
    union = (box1[2] - box1[0] + 1) * (box1[3] - box1[1] + 1) + (
        box2[2] - box2[0] + 1) * (box2[3] - box2[1] + 1) - intersection
    return intersection / union

def get_most_promised_number(inp):
    inp = inp.split()
    # keep only numbers
    out = []
    for _ in inp:
        try:
            out.append(float(_))
        except:
            pass
    # find the most promised number
    if out:
        return max(out)
    else:
        return None