from pydantic import BaseModel, Field
from typing import List, Optional

class BudgetColumn(BaseModel):
    """Represents a budget column in the financial table"""
    index: int = Field(description="Column index (starting from 1)")
    name: str = Field(description="Name of the budget column")

class PeriodData(BaseModel):
    """Represents a period in the financial table"""
    period: str = Field(description="Period of the budget (e.g., '2023', '2024')")
    actual_budget_column: BudgetColumn = Field(
        description="Column containing actual budget values"
    )
    reference_budget_columns: List[BudgetColumn] = Field(
        description="List of columns containing reference budget values (Budget, Previous year columns, etc.)"
    )

class TableData(BaseModel):
    """Represents a single financial table extracted from the document"""
    table_id: int = Field(description="Unique identifier for the table in the document")
    table_title: Optional[str] = Field(None, description="Title or caption of the table if available")
    periods: List[PeriodData] = Field(
        description="List of periods and their corresponding values in the table"
    )

class Case1Analysis(BaseModel):
    """Complete analysis of financial tables in the document"""
    tables: List[TableData] = Field(description="List of financial tables found in the document")