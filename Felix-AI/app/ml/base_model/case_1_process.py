import os

import openai
import json
import pandas as pd
import litellm
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from app.ml.llm import Case1Analysis
from app.ml.base_model.util import json_extractor
from app.ml.base_model.base_process import BaseProcess
from app.models.request import Request, RequestStatus
from app.api.services.utils import notify_status

class Case1Process(BaseProcess):
    """
    Process for Case 1.
    """
    def __init__(self, session: AsyncSession, request_id: str):
        super().__init__(session, request_id)
        self.client = openai.AsyncAzureOpenAI(
            api_version=os.getenv("AZURE_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_API_BASE"),
            api_key=os.getenv("AZURE_API_KEY")
        )

    async def llm_analyze(self):
        """
        Analyze the document using LLM.
        """

        # responses = await asyncio.gather(
        #     *[
        #         self.client.beta.chat.completions.parse(
        #             model=f"{os.getenv('AZURE_OPENAI_MODEL_NAME')}",
        #             messages=[
        #                 {
        #                     "role": "user", 
        #                     "content": [
        #                         # {"type": "text", "text": "Analyze this financial image and extract: 1) Columns containing actual budget values, 2) Columns containing reference budget values (Budget, Previous year columns, etc.). Return the column indices (starting from 1) for each category."},
        #                         # {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self.page_base64[i]}"}},
        #                         {
        #                             "type": "text",
        #                             "text": (
        #                                 "Analyze this financial table and extract: "
        #                                 "1) Columns containing actual budget values, "
        #                                 "2) Columns containing reference budget values (Budget, Previous year columns, etc.). "
        #                                 "Return the column indices (starting from 1) for each category.\n\n"
        #                                 f"Table:\n{self.page_markdowns[i]}"
        #                             ),
        #                         },
        #                         {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self.page_base64[i]}"}},
        #                     ]
        #                 }
        #             ],
        #             max_tokens=1000,
        #             temperature=0.00000000000000000001,
        #             response_format=Case1Analysis,
        #             timeout=600,
        #         )
        #         for i in range(len(self.page_base64))
        #     ]
        # )

        responses = []
        for i in range(len(self.page_markdowns)):
            content = [
                {
                    "type": "text",
                    "text": (
                        "Analyze this financial table, and the image and extract: "
                        "1) Columns containing actual budget values, "
                        "2) Columns containing reference budget values (Budget, Previous year columns, etc.). "
                        "When the table shows multiple time periods (e.g., years or quarters) with one column per period, identify the most recent period column as the 'actual budget column' and all earlier period columns as 'reference budget columns'."
                        "Return the column indices (starting from 1) for each category.\n\n"
                        f"Table:\n{self.page_markdowns[i]}"
                    ),
                },
                # {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self.page_base64[i]}"}},
            ]
            for page_index in range(self.table_page_index[i][0], self.table_page_index[i][1]+1):
                content.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{self.page_base64[page_index]}"},
                    }
                )
            response = await self.client.beta.chat.completions.parse(
                model=f"{os.getenv('AZURE_OPENAI_MODEL_NAME')}",
                messages=[
                    {
                        "role": "user", 
                        "content": content
                    }
                ],
                max_tokens=1000,
                temperature=0.00000000000000000001,
                response_format=Case1Analysis,
                timeout=600,
            )
            responses.append(response)

        responses = [json_extractor(response.choices[0].message.content) for response in responses]

        return responses
    
    def save_to_excel(self):
        """
        Saves the analysis df to an Excel file.
        """

        workbook = pd.ExcelWriter("app/resources/analysis_data.xlsx", engine='xlsxwriter')

        # Write each DataFrame to a separate sheet
        for i, df in enumerate(self.layout_data_dfs):
            sheet_name = f"Table {i + 1}"
            df.to_excel(workbook, sheet_name=sheet_name, index=False)

        # workbook.save()
        workbook.close()

    async def run(self):
        """
        Run the process.
        """
        # update request status to Processing
        request = await self.session.get(Request, self.request_id)

        if not request:
            raise ValueError("Request not found")

        setattr(request, "status", RequestStatus.PROCESSING)
        await self.session.commit()
        notify_status(
            request_id=self.request_id,
            status="PROCESSING",
        )

        await self.download()

        # self.local_file_path = "app/resources/84bd22948d3a3097e73df2d36211c7d9_Case 1 All.pdf"
        self.page_bytes, self.page_base64 = self.split_bytes()
        layout_data = await self.extract_layout_data(self.page_bytes)

        analysis_datas = await self.llm_analyze()

        # Initialize a global table index for the flattened layout_data
        global_table_index = 0

        self.layout_data_dfs = []

        # Iterate over all pages and their respective tables
        for page_index, analysis_data in enumerate(analysis_datas):
            for table_index, table_analysis in enumerate(analysis_data["tables"]):
                # Use the global_table_index to access the correct table in layout_data
                current_table = layout_data[global_table_index]

                # Collect all column indices that need to be numeric (actual and reference columns)
                numeric_column_indices = []
                for period in table_analysis["periods"]:
                    # Add actual column index
                    actual_col_index = period["actual_budget_column"]["index"] - 1
                    numeric_column_indices.append(actual_col_index)
                    
                    # Add reference column indices
                    for ref_col in period["reference_budget_columns"]:
                        ref_col_index = ref_col["index"] - 1
                        numeric_column_indices.append(ref_col_index)

                # Get unique column indices
                numeric_column_indices = list(set(numeric_column_indices))

                # Filter rows where all specified column values can be converted to float
                filtered_rows = []
                headers = current_table[0]  # Save the headers

                for row in current_table[1:]:  # Skip the header row
                    valid_row = False
                    for idx in numeric_column_indices:
                        if idx < len(row):  # Ensure index is valid
                            try:
                                float(row[idx])
                                valid_row = True
                            except (ValueError, TypeError):
                                pass
                    
                    if valid_row:
                        filtered_rows.append(row)

                # Replace the data rows with filtered rows
                layout_data[global_table_index] = [headers] + filtered_rows

                column_names = layout_data[global_table_index][0]
                # Handle case empty column names
                column_names = [f"Column {i+1}" if name == "" else name for i, name in enumerate(column_names)]
                layout_data_df = pd.DataFrame(layout_data[global_table_index][1:], columns=column_names)

                # For each period in the analysis
                for period in table_analysis["periods"]:
                    # Get the actual column (we know there's only one)
                    actual_col = period["actual_budget_column"]
                    reference_columns = period["reference_budget_columns"]
                    
                    # Get the actual column name from the index
                    actual_col_index = actual_col["index"] - 1
                    column_names[actual_col_index] = actual_col["name"]
                    if actual_col_index < 0 or actual_col_index >= len(layout_data_df.columns):
                        continue  # Skip if index is out of bounds
                    actual_col_name = layout_data_df.columns[actual_col_index]
                    
                    # For each reference column, calculate variance against the single actual column
                    for ref_col in reference_columns:
                        # Get the reference column name
                        ref_col_index = ref_col["index"] - 1
                        column_names[ref_col_index] = ref_col["name"]
                        if ref_col_index < 0 or ref_col_index >= len(layout_data_df.columns):
                            continue  # Skip if index is out of bounds
                        ref_col_name = layout_data_df.columns[ref_col_index]
                        
                        # Create a new column name for the variance
                        variance_col_name = f"{period['period']}_{actual_col['name']}_vs_{ref_col['name']}"

                        column_names.append(variance_col_name)
                        
                        # Calculate the variance (actual - reference)
                        try:
                            layout_data_df[variance_col_name] = (abs((pd.to_numeric(layout_data_df[actual_col_name], errors='coerce') \
                                                                  - pd.to_numeric(layout_data_df[ref_col_name], errors='coerce')) \
                                                                / abs(pd.to_numeric(layout_data_df[ref_col_name], errors='coerce')) * 100)) \
                                                                    .replace([float('inf'), -float('inf')], pd.NA)
                        except Exception as e:
                            print(f"Error calculating variance for {variance_col_name}: {e}")
                            continue

                # Increment the global table index
                global_table_index += 1

                # Rename columns with column_names
                layout_data_df.columns = column_names

                self.layout_data_dfs.append(layout_data_df)

        self.save_to_excel()

        setattr(request, "status", RequestStatus.DONE)
        await self.session.commit()
        notify_status(
            request_id=self.request_id,
            status="DONE",
        )
        return [json.loads(layout_data_df.to_json(orient="records")) for layout_data_df in self.layout_data_dfs]