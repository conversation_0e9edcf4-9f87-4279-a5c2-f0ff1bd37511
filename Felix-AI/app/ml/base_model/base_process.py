import os
import fitz
import secrets
import base64
from io import BytesIO
from dotenv import load_dotenv

import asyncio
from PIL import Image
from openpyxl import Workbook
import tabulate
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from azure.storage.blob import B<PERSON>bServiceClient
from azure.core.credentials import AzureKeyCredential
from azure.ai.formrecognizer.aio import DocumentAnalysisClient

from app.models.file import File
from app.models.request import RequestStatus, Request
from app.ml.base_model.util import convert_to_float

load_dotenv()

class BaseProcess:
    """
    Base class for all processes.
    """
    def __init__(self, session: AsyncSession, request_id: str):
        DOCUMENT_INTELLIGENCE_ENDPOINT = os.getenv("DOCUMENT_INTELLIGENCE_ENDPOINT")
        DOCUMENT_INTELLIGENCE_KEY = os.getenv("DOCUMENT_INTELLIGENCE_KEY")
        
        self.session = session
        self.request_id = request_id
        self.blob_service_client = BlobServiceClient.from_connection_string(os.getenv("BLOB_STORAGE_CONNECTION_STRING"))
        self.idp_document_analysis_client = DocumentAnalysisClient(
            endpoint=DOCUMENT_INTELLIGENCE_ENDPOINT, credential=AzureKeyCredential(DOCUMENT_INTELLIGENCE_KEY)
        )

    async def download(self):
        """
        Download the model from the cloud storage.
        """
        # Get the file URL from the database
        statement = select(File).where(File.request_id == self.request_id)
        file = await self.session.execute(statement)
        file = file.scalars().first()

        if not file:
            raise ValueError("File not found")
    
        url = file.blob_url
        
        # Split the URL to get the container name and blob name
        # Example URL: https://<storage_account_name>.blob.core.windows.net/<container_name>/<blob_name>
        url = url.split("/")
        container_name = url[-2].replace("%20", " ")
        blob = url[-1].replace("%20", " ")

        blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=blob)
        # write to local, random an id for file path
        self.local_file_path = f"app/resources/{secrets.token_hex(16)}_{blob}"
        with open(self.local_file_path, "wb") as data:
            # Download the blob to a local file
            blob_data = blob_client.download_blob()
            data.write(blob_data.readall())

    def split_bytes(self):
        """
        Splits a PDF file into individual pages and returns each page as a byte array.
        Args:
        Returns:
            list: A list of byte arrays, each representing a single page of the PDF.
        """

        # Open the PDF
        pdf_document = fitz.open(self.local_file_path, filetype="pdf")
        byte_pages = []
        base64_pages = []

        # Process each page
        for page_number in range(len(pdf_document)):
            new_pdf = fitz.open()
            new_pdf.insert_pdf(pdf_document, from_page=page_number, to_page=page_number)
            
            pdf_buffer = BytesIO()
            new_pdf.save(pdf_buffer, garbage=4)
            pdf_buffer.seek(0)

            byte_pages.append(pdf_buffer.getvalue())

            # get the base64 of the image
            pix = new_pdf[0].get_pixmap(dpi=180)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            img.info["dpi"] = (180, 180)
            img_buffer = BytesIO()
            img.save(img_buffer, format="PNG")
            img_buffer.seek(0)
            base64_image = base64.b64encode(img_buffer.getvalue()).decode("utf-8")
            base64_pages.append(base64_image)
            
            # Close the temporary PDF
            new_pdf.close()

        # Close the original PDF
        pdf_document.close()

        return byte_pages, base64_pages

    async def azure_prebuilt_idp(self, byte):
        """
        Analyzes a document using Azure's prebuilt IDP (Intelligent Document Processing) service.
        Args:
            byte (bytes): The byte of the document to be analyzed.
        Returns:
            result: The result of the document analysis.
        """

        poller = await self.idp_document_analysis_client.begin_analyze_document(
            "prebuilt-layout",
            byte,
        )
        result = await poller.result()
        return result

    # async def extract_layout_data(self, page_bytes):
    #     """
        
    #     Extracts layout data from the provided byte arrays of PDF pages.
    #     Args:
    #         page_bytes (list): A list of byte arrays representing the PDF pages.
    #     Returns:
    #         list: A list of extracted layout data for each page.
    #     """
    #     layout_datas = await asyncio.gather(
    #         *[self.azure_prebuilt_idp(page) for page in page_bytes]
    #     )

    #     results = [self.preprocess_layout_data(layout_data) for layout_data in layout_datas]

    #     self.page_markdowns = [
    #         " ".join(
    #             [
    #                 tabulate.tabulate(
    #                     t,
    #                     # tablefmt="grid",
    #                 )
    #                 for t in page
    #             ]
    #         )
    #         for page in results
    #     ]

    #     # flatten the tables all pages into a list of tables
    #     results = [table for page in results for table in page]

    #     return results

    async def extract_layout_data(self, page_bytes):
        """
        Extracts layout data from the provided byte arrays of PDF pages and merges cross-page tables.
        
        Args:
            page_bytes (list): A list of byte arrays representing the PDF pages.
        Returns:
            list: A list of extracted layout data with cross-page tables merged.
        """
        layout_datas = await asyncio.gather(
            *[self.azure_prebuilt_idp(page) for page in page_bytes]
        )

        # Process individual page tables
        results = [self.preprocess_layout_data(layout_data) for layout_data in layout_datas]
        
        # Identify and merge cross-page tables
        merged_results = self.merge_cross_page_tables(results)
        
        self.table_page_index = [
            (start, end) for _, start, end in merged_results
        ]

        # Generate a single markdown string with all merged tables
        self.page_markdowns = [
            " ".join([tabulate.tabulate(table) for table, _, _ in merged_results])
        ]

        # Return the flattened list of merged tables
        return [table for table, _, _ in merged_results]

    def merge_cross_page_tables(self, page_tables):
        """
        Identifies and merges tables that span across multiple pages.
        
        Args:
            page_tables (list): A list of lists, where each inner list contains tables from a single page.
        Returns:
            list: A list of tuples (table, start_page, end_page) with cross-page tables merged.
        """
        merged_results = []
        
        # Create a copy of page_tables to track which tables we've processed
        remaining_tables = []
        for page in page_tables:
            remaining_tables.append(page.copy())
        
        # Process one page at a time
        for i in range(len(remaining_tables)):
            current_page = remaining_tables[i]
            
            # Process each table on the current page
            j = 0
            while j < len(current_page):
                current_table = current_page[j]
                start_page = i
                end_page = i
                merged_table = current_table
                
                # Look for potential merges with tables on subsequent pages
                for k in range(i + 1, len(remaining_tables)):
                    next_page = remaining_tables[k]
                    merged_with_next = False
                    
                    l = 0
                    while l < len(next_page):
                        next_table = next_page[l]
                        
                        # Check if tables can be merged vertically
                        if self.can_merge_vertically(merged_table, next_table):
                            merged_table = self.merge_tables_vertically(merged_table, next_table)
                            end_page = k
                            next_page.pop(l)  # Remove the merged table
                            merged_with_next = True
                            # Don't increment l as we removed an item
                            continue
                        
                        # Check if tables can be merged horizontally
                        elif self.can_merge_horizontally(merged_table, next_table):
                            merged_table = self.merge_tables_horizontally(merged_table, next_table)
                            end_page = k
                            next_page.pop(l)  # Remove the merged table
                            merged_with_next = True
                            # Don't increment l as we removed an item
                            continue
                        
                        l += 1
                    
                    # If no merge occurred with this page, stop looking further
                    if not merged_with_next:
                        break
                
                # Add the merged table to results with its page span
                merged_results.append((merged_table, start_page, end_page))
                
                # Remove the processed table
                current_page.pop(j)
                # Don't increment j since we removed an item
        
        return merged_results

    def can_merge_vertically(self, table1, table2):
        """
        Determines if two tables can be merged vertically.
        
        Args:
            table1 (list): The first table.
            table2 (list): The second table.
        Returns:
            bool: True if tables can be merged vertically, False otherwise.
        """
        # Check if tables have the same number of columns
        if len(table1[0]) != len(table2[0]):
            return False
        
        # Additional checks could be added based on table content or structure
        # For example, check header similarity or content types
        
        return True

    def can_merge_horizontally(self, table1, table2):
        """
        Determines if two tables can be merged horizontally.
        
        Args:
            table1 (list): The first table.
            table2 (list): The second table.
        Returns:
            bool: True if tables can be merged horizontally, False otherwise.
        """
        # Check if tables have the same number of rows
        if len(table1) != len(table2):
            return False
        
        # Additional checks could be added based on table positioning
        # For example, check if table1 is on the right edge of page and table2 is on left edge
        
        return False  # Default to False for horizontal merging in this implementation

    def merge_tables_vertically(self, table1, table2):
        """
        Merges two tables vertically.
        
        Args:
            table1 (list): The first table.
            table2 (list): The second table.
        Returns:
            list: The merged table.
        """
        # Skip the header row of table2 if it exists and is similar to table1's header
        start_idx = 1 if len(table2) > 1 else 0
        
        # Combine the tables
        return table1 + table2[start_idx:]

    def merge_tables_horizontally(self, table1, table2):
        """
        Merges two tables horizontally.
        
        Args:
            table1 (list): The first table.
            table2 (list): The second table.
        Returns:
            list: The merged table.
        """
        merged_table = []
        
        for i in range(len(table1)):
            merged_row = table1[i] + table2[i]
            merged_table.append(merged_row)
        
        return merged_table

    def preprocess_layout_data(self, layout_data):
        """
        Preprocesses the layout data extracted from the document.
        Args:
            layout_data (list): The layout data to be preprocessed.
        Returns:
            list: A list of preprocessed layout data.
        """
        tables = []
        for table in layout_data.tables:
            table_data = [["" for i in range(table.column_count)] for j in range(table.row_count)]
            if len(table.cells) == 0:
                continue

            for cell in table.cells:
                table_data[cell.row_index][cell.column_index] = convert_to_float(cell.content)

            # Remove rows where all cell is in type string, except first row
            table_data = [table_data[0]] + [
                row for row in table_data[1:] if any(isinstance(cell, float) for cell in row)
            ]

            tables.append(table_data)

        # Remove tables with less than 3 rows
        tables = [table for table in tables if len(table) > 2]
        return tables

    def save_to_excel(self, data):
        """
        Saves the extracted data to an Excel file.
        Args:
            data (list): The data to be saved to the Excel file.
        """
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Table 1"

        # Write 1st table
        for i, row in enumerate(data[0]):
            worksheet.append(row)

        # Write remaining tables
        for i, table in enumerate(data[1:]):
            worksheet = workbook.create_sheet(title=f"Table {i + 2}")
            for row in table:
                worksheet.append(row)

        workbook.save("app/resources/extracted_data.xlsx")

    async def llm_analyze(self):
        raise NotImplementedError("LLM analysis is not implemented.")

    async def run(self):
        """
        Run the process.
        """
        # update request status to Processing
        request = await self.session.get(Request, self.request_id)

        if not request:
            raise ValueError("Request not found")

        setattr(request, "status", RequestStatus.PROCESSING)
        await self.session.commit()

        await self.download()

        self.page_bytes, self.page_base64 = self.split_bytes()
        layout_data = await self.extract_layout_data(self.page_bytes)

        self.save_to_excel(layout_data)

        setattr(request, "status", RequestStatus.DONE)
        await self.session.commit()