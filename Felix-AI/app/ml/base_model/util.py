import re
import json

def convert_to_float(value):
    """
    Convert a string value to a float, handling commas and other formatting issues.
    """
    try:
        # If value contains at least 1 alphabetic word, return it as is
        if re.search(r"[a-zA-Z]", value):
            return value
        # Remove space and new line characters
        value = re.sub(r"[\s\n]", "", value)
        # only keep the last dot character
        number = re.sub(r"\.(?=.*\.)", "", value)
        number = re.findall(r"-?\d+(?:,\d+)+(?:\.\d+)?|-?\d+(?:\.\d+)?", number)
        if len(number) > 0:
            number = number[0]
        return float(number.replace(",", "")) if number else value
    except:
        return value

def json_extractor(str_input):
    try:
        json2dict = json.loads(str_input)
        return json2dict
    except Exception as e:
        print(f"Error: {e}")
        return None
