import logging
import os
from contextvars import <PERSON><PERSON><PERSON><PERSON>
from typing import Callable
from dotenv import load_dotenv

from sqlmodel import SQLModel, create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, scoped_session

db_state_default = {"closed": None, "conn": None, "ctx": None, "transactions": None}
db_state = ContextVar("db_state", default=db_state_default.copy())

load_dotenv()

# database
connect_args = {}

# For sync operations
database_url = os.getenv("DATABASE_URL", "postgresql+psycopg2://postgres:postgres@localhost:5432/felix_ai")
if "postgresql+asyncpg" in database_url:
    # Convert asyncpg URL to psycopg2 for sync operations
    database_url = database_url.replace("postgresql+asyncpg", "postgresql+psycopg2")

# Create sync engine
engine = create_engine(
    database_url,
    connect_args=connect_args,
    pool_size=20,
    pool_recycle=3600,
    echo=False,
)
session = scoped_session(sessionmaker(bind=engine))

# For async operations
async_database_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/felix_ai")
# Make sure we're using the asyncpg driver for async operations
if "postgresql+" in async_database_url and "asyncpg" not in async_database_url:
    async_database_url = async_database_url.replace("postgresql+", "postgresql+asyncpg://")
elif "postgresql://" in async_database_url:
    async_database_url = async_database_url.replace("postgresql://", "postgresql+asyncpg://")

# Create async engine with appropriate settings for async
async_engine = create_async_engine(
    async_database_url,
    connect_args=connect_args,
    pool_pre_ping=True,
    pool_size=20,
    pool_recycle=3600,
    echo=False,
)

# 创建session元类
async_session_local: Callable[..., AsyncSession] = sessionmaker(
    class_=AsyncSession,
    bind=async_engine,
    expire_on_commit=False,
)


async def create_db_and_tables():
    logging.debug("Creating database and tables")
    import app.models  # noqa

    # SQLModel.metadata.create_all(async_engine)
    async with async_engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
    logging.debug("Database and tables created successfully")
