"""Start Application."""
import random
import string
import time
import os

import hypercorn.trio
import trio
from fastapi import FastAPI, Security
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.exceptions import HTTPException
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request
from starlette.responses import HTMLResponse, Response
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from typing import AsyncGenerator

# from app.api.database.migrate.init_super_user import init_super_user
from app.api.errors.http_error import http_error_handler
from app.api.errors.validation_error import http422_error_handler
from app.api.routes.api import app as api_router
from app.api.routes import authentication
from app.core.config import ALLOWED_HOSTS, API_PREFIX, DEBUG, PROJECT_NAME, VERSION
from app.core.constant import APP_HOST, APP_PORT
from app.logger.logger import custom_logger
from app.providers import pagination_provider
from app.providers.database import create_db_and_tables
from app.api.database.models.auth import azure_scheme

load_dotenv()

class LoggingMiddleware(BaseHTTPMiddleware):
    """Logging All API request."""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Dispatch."""
        idem = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        custom_logger.info(f"rid={idem} start request {request.method} {request.url.path}")
        start_time = time.time()

        # Log the request
        custom_logger.info("Received request: %s %s", request.method, request.url)
        custom_logger.debug("Request headers: %s", request.headers)
        custom_logger.debug("Request body: %s", await request.body())

        # Call the next middleware or route handler
        response = await call_next(request)

        process_time = (time.time() - start_time) * 1000
        formatted_process_time = '{0:.2f}'.format(process_time)

        custom_logger.info(
            "rid=%s method=%s path=%s completed_in=%sms status_code=%s",
            idem, request.method, request.url.path, formatted_process_time, response.status_code,
        )
        custom_logger.info("Response status code: %s", response.status_code)
        custom_logger.debug("Response headers: %s", response.headers)

        return response


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Load OpenID config on startup.
    """
    try:
        # Check if azure_scheme has openid_config attribute
        if hasattr(azure_scheme, 'openid_config') and hasattr(azure_scheme.openid_config, 'load_config'):
            custom_logger.info("Loading OpenID config...")
            await azure_scheme.openid_config.load_config()
        else:
            custom_logger.info("Using mock Azure authentication - no OpenID config to load")
    except Exception as e:
        custom_logger.error(f"Error loading OpenID config: {e}")

    # Continue with application startup
    yield


def get_application() -> FastAPI:
    """Get application."""
    # init_super_user()

    application = FastAPI(
        swagger_ui_oauth2_redirect_url=os.getenv("ENTRAID_REDIRECT_URI"),
        swagger_ui_init_oauth={
            'usePkceWithAuthorizationCodeGrant': True,
            'clientId': os.getenv("OPENAPI_CLIENT_ID"),
        },
        title=PROJECT_NAME,
        debug=DEBUG,
        version=VERSION,
        lifespan=lifespan)

    application.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    application.add_middleware(LoggingMiddleware)
    application.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])

    application.add_exception_handler(HTTPException, http_error_handler)
    application.add_exception_handler(RequestValidationError, http422_error_handler)

    application.include_router(api_router, prefix=API_PREFIX)
    application.include_router(authentication.router, tags=["Authentication"])

    pagination_provider.register(application)

    @application.get("/logger", response_class=HTMLResponse, deprecated=False)
    async def get_logger():
        try:
            with open("app/logger/logger.log", "r") as f:
                log_str = f.read()
                log_html = f"<pre>{log_str}</pre>"
                return log_html
        except FileNotFoundError:
            return HTMLResponse(content="<pre>Log file not found</pre>", status_code=404)

    @application.on_event("startup")
    async def startup_event():
        custom_logger.info("Starting up...")
        try:
            # Create database tables
            custom_logger.info("Creating database tables...")
            await create_db_and_tables()
            custom_logger.info("Database tables created successfully")
        except Exception as e:
            custom_logger.error(f"Error creating database tables: {e}")
            # Continue anyway, as tables might already exist

    # This lifespan context manager is already defined at the module level

    return application


async def app_handler(scope, receive, send):
    await app(scope, receive, send)


async def main():
    config = hypercorn.trio.Config.from_mapping(
        bind=[f"{APP_HOST}:{APP_PORT}"],
        workers=1,
    )
    await hypercorn.trio.serve(app_handler, config)


app = get_application()

if __name__ == "__main__":
    trio.run(main)
