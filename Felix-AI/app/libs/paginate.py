import logging
import os
from typing import TypeVar, Any, Optional, Generic, List, Sequence

# Configure logging
logger = logging.getLogger(__name__)

# Check if we should use mock pagination
USE_MOCK_PAGINATION = os.getenv("USE_MOCK_PAGINATION", "true").lower() == "true"

try:
    if USE_MOCK_PAGINATION:
        # Use mock pagination
        logger.info("Using mock pagination")
        from app.libs.mock_paginate import CommonPage, CursorPage, cursor_page
        from fastapi import Query
        from sqlmodel import asc, desc
        from sqlalchemy.ext.asyncio import AsyncSession

        # Define mock classes for compatibility
        class AbstractPage:
            pass

        class AbstractParams:
            pass

        class CursorRawParams:
            pass

        class Cursor:
            pass
    else:
        # Use real pagination
        logger.info("Using real pagination")
        from fastapi import Query
        from fastapi_pagination.bases import AbstractPage, AbstractParams, CursorRawParams
        from fastapi_pagination.cursor import encode_cursor
        from fastapi_pagination.ext.sqlmodel import paginate
        from fastapi_pagination.types import Cursor
        from fastapi_pagination.utils import verify_params, create_pydantic_model
        from sqlmodel import asc, desc
        from sqlalchemy.ext.asyncio import AsyncSession
except ImportError as e:
    logger.error(f"Error importing pagination dependencies: {e}")
    logger.info("Falling back to mock pagination")
    USE_MOCK_PAGINATION = True
    from app.libs.mock_paginate import CommonPage, CursorPage, cursor_page
    from fastapi import Query
    from sqlmodel import asc, desc
    from sqlalchemy.ext.asyncio import AsyncSession

    # Define mock classes for compatibility
    class AbstractPage:
        pass

    class AbstractParams:
        pass

    class CursorRawParams:
        pass

    class Cursor:
        pass

from app.models.base_model import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


if not USE_MOCK_PAGINATION:
    # Only define these classes if using real pagination
    class CursorParams(BaseModel, AbstractParams):
        limit: int = Query(20, ge=1, le=100, description="Page offset")
        order: str = Query(default="desc", description="Sort order")
        after: Optional[str] = Query(None, description="Page after")
        before: Optional[str] = Query(None, description="Page before")

        def to_raw_params(self) -> CursorRawParams:
            return CursorRawParams(cursor=None, size=self.limit, include_total=True)


    class RealCommonPage(AbstractPage[ModelType], Generic[ModelType]):
        __params_type__ = CursorParams

        object: str = "list"
        data: List[ModelType] = []
        first_id: Optional[str] = ""
        last_id: Optional[str] = ""
        has_more: bool = False

        @classmethod
        def create(
            cls,
            items: Sequence[ModelType],
            params: CursorParams,
            *,
            current: Optional[Cursor] = None,
            current_backwards: Optional[Cursor] = None,
            next_: Optional[Cursor] = None,
            previous: Optional[Cursor] = None,
            **kwargs: Any,
        ) -> AbstractPage[ModelType]:
            next_page = encode_cursor(next_)
            return create_pydantic_model(
                RealCommonPage,
                next_page=next_page,
                first_id=str(items[0].id) if items else None,
                last_id=str(items[len(items) - 1].id) if items else None,
                has_more=False if next_page is None else True,
                data=list(items),
            )


    async def real_cursor_page(query: Any, db: AsyncSession) -> RealCommonPage[ModelType]:
        params, _ = verify_params(None, "cursor")
        model = query._propagate_attrs["plugin_subject"].class_

        logger.debug(
            f"Page model={model}, sort={params.order}, filter_parameters=before:{params.before}, after:{params.after}",
        )

        if params.before is not None:
            if params.order.upper() == "DESC":
                query = query.where(model.id > params.before)
            else:
                query = query.where(model.id < params.before)
        if params.after is not None:
            if params.order.upper() == "DESC":
                query = query.where(model.id < params.after)
            else:
                query = query.where(model.id > params.after)

        if params.order.upper() == "DESC":
            query = query.order_by(desc(model.__dict__["id"]))
        else:
            query = query.order_by(asc(model.__dict__["id"]))

        return await paginate(db, query)

    # Set CommonPage to RealCommonPage
    CommonPage = RealCommonPage
    cursor_page = real_cursor_page
else:
    # For mock pagination, we already imported CommonPage and cursor_page from mock_paginate
    # Define a mock CursorParams class for compatibility
    class CursorParams(BaseModel):
        limit: int = Query(20, ge=1, le=100, description="Page offset")
        order: str = Query(default="desc", description="Sort order")
        after: Optional[str] = Query(None, description="Page after")
        before: Optional[str] = Query(None, description="Page before")

        def to_raw_params(self):
            return None


# Define a mock async cursor_page function that uses the mock implementation
async def async_cursor_page(query: Any, db: AsyncSession) -> CommonPage[ModelType]:
    """Mock cursor page function for development."""
    logger.info("Using mock cursor_page function")

    # Create a list of mock items
    items = []
    try:
        # Try to get some items from the query
        result = await db.execute(query.limit(20))
        items = result.scalars().all()
    except Exception as e:
        logger.error(f"Error executing query: {e}")

    # Use the mock cursor_page function
    if USE_MOCK_PAGINATION:
        # For mock pagination
        return CommonPage(
            items=items,
            total=len(items),
            page=1,
            size=20
        )
    else:
        # For real pagination
        params = CursorParams(limit=20, order="desc")
        return CommonPage.create(
            items=items,
            params=params,
            next_=None,
            data=items
        )

# Override cursor_page with async_cursor_page
cursor_page = async_cursor_page
