"""Define Logger for log message."""

import logging
import os
from datetime import datetime

# Create logger directory if it doesn't exist
os.makedirs("app/logger", exist_ok=True)

# Check if we should use mock logger
USE_MOCK_LOGGER = os.getenv("USE_MOCK_LOGGER", "true").lower() == "true"

try:
    from app.core.config import LOGGING_LEVEL
except ImportError:
    # Fallback to INFO level
    LOGGING_LEVEL = logging.INFO


class UvicornFormatter(logging.Formatter):
    """Uvicorn Formatter."""

    FORMAT = (
        "\033[38;5;244m%(asctime)s\033[0m"
        " | "
        "%(levelname)-7s"
        " | "
        "\033[38;5;214m%(name)s\033[0m"
        " : "
        "\033[38;5;111m%(message)s\033[0m"
    )

    LEVEL_COLORS = {
        "DEBUG": "\033[38;5;32m",
        "INFO": "\033[38;5;36m",
        "WARNING": "\033[38;5;221m",
        "ERROR": "\033[38;5;196m",
        "CRITICAL": "\033[48;5;196;38;5;231m",
    }

    def format(self, record):
        """Config format"""
        levelname = record.levelname
        level_color = self.LEVEL_COLORS.get(levelname, "")
        record.levelname = f"{level_color}{levelname}\033[0m"
        record.asctime = datetime.fromtimestamp(record.created).strftime("%Y-%m-%d %H:%M:%S.%f")
        return super().format(record)


def configure_logging():
    """Initialize logging defaults for Project.

    This function does:
    - Assign INFO and DEBUG level to logger file handler and console handler.

    Returns:
        Logger.
    """
    logger = logging.getLogger()
    logger.setLevel(LOGGING_LEVEL)

    try:
        # Create a file handler with a lower log level
        file_handler = logging.FileHandler("app/logger/logger.log")
        file_handler.setLevel(LOGGING_LEVEL)

        # Create a console handler with a higher log level
        console_handler = logging.StreamHandler()
        console_handler.setLevel(LOGGING_LEVEL)

        # Create a formatter and add it to the handlers
        default_formatter = logging.Formatter(
            "[%(asctime)s] [%(levelname)s] [%(name)s] "
            "[%(funcName)s():%(lineno)s] [PID:%(process)d TID:%(thread)d] %(message)s",
            "%d/%m/%Y %H:%M:%S",
        )

        file_handler.setFormatter(default_formatter)
        console_handler.setFormatter(UvicornFormatter(UvicornFormatter.FORMAT))

        if logger.hasHandlers():
            logger.handlers.clear()

        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
    except Exception as e:
        # Fallback to basic logging
        logging.error(f"Error configuring logger: {e}")
        console_handler = logging.StreamHandler()
        console_handler.setLevel(LOGGING_LEVEL)
        logger.addHandler(console_handler)

    return logger


# Initialize the logger
try:
    custom_logger = configure_logging()

    # Create an empty log file if it doesn't exist
    if not os.path.exists("app/logger/logger.log"):
        with open("app/logger/logger.log", "w") as f:
            f.write("Log file initialized\n")
except Exception as e:
    # Fallback to basic logger
    logging.error(f"Error initializing logger: {e}")
    custom_logger = logging.getLogger("custom_logger")
