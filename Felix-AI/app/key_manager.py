from collections import deque
import threading

class APIKeyManager:
    def __init__(self, api_keys: list):
        self.keys = api_keys
        self.key_queue = deque(api_keys)  # For round-robin
        self.failed_keys = set()
        self.lock = threading.Lock()
        
    def get_key(self, strategy: str = "round-robin") -> str:
        with self.lock:
            if not self.keys:
                raise ValueError("No API keys available")
                
            if strategy == "round-robin":
                return self._round_robin()
            elif strategy == "least-used":
                return self._least_used()
            else:
                raise ValueError("Invalid strategy")

    def _round_robin(self) -> str:
        key = self.key_queue.popleft()
        self.key_queue.append(key)
        return key

    def _least_used(self) -> str:
        raise NotImplementedError