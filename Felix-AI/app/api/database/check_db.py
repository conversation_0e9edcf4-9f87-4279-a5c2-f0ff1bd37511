"""Check database connectivity."""
import asyncio
import logging
import os
import sys
import asyncpg

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

async def check_db_connection():
    """Check if we can connect to the database."""
    logger.info("Checking database connection...")
    
    # Get database URL from environment variable
    db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/felix_ai")
    
    # Convert SQLAlchemy URL to asyncpg URL if needed
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
    
    # Extract connection parameters
    db_parts = db_url.replace("postgresql://", "").split("/")
    db_name = db_parts[1] if len(db_parts) > 1 else "felix_ai"
    
    auth_parts = db_parts[0].split("@")
    host_port = auth_parts[1] if len(auth_parts) > 1 else "localhost:5432"
    
    user_pass = auth_parts[0].split(":")
    user = user_pass[0]
    password = user_pass[1] if len(user_pass) > 1 else ""
    
    host_parts = host_port.split(":")
    host = host_parts[0]
    port = int(host_parts[1]) if len(host_parts) > 1 else 5432
    
    logger.info(f"Connecting to PostgreSQL at {host}:{port} as {user}")
    
    try:
        # Try to connect to the database
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db_name
        )
        
        # Run a simple query
        version = await conn.fetchval("SELECT version()")
        logger.info(f"Successfully connected to the database: {version}")
        
        # Close the connection
        await conn.close()
        return True
    except Exception as e:
        logger.error(f"Error connecting to the database: {e}")
        return False

def main():
    """Run the database connection check."""
    try:
        result = asyncio.run(check_db_connection())
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"Unhandled exception in database connection check: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
