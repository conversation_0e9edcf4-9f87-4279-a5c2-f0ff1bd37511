"""Fix the requests table by adding missing columns."""
import asyncio
import logging
import subprocess
import sys

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def run_command(command):
    """Run a shell command and log the output."""
    try:
        logger.info(f"Running command: {command}")
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True
        )
        logger.info(f"Command output: {result.stdout}")
        if result.stderr:
            logger.warning(f"Command error output: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        logger.error(f"Error running command: {e}")
        return False

async def fix_requests_table():
    """Fix the requests table by adding missing columns."""
    logger.info("Fixing requests table...")

    # Apply the migration
    if not run_command("cd /app && python -m alembic upgrade head"):
        logger.error("Failed to apply migration.")
        return False

    logger.info("Requests table fixed successfully.")
    return True

def main():
    """Run the fix."""
    try:
        result = asyncio.run(fix_requests_table())
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"Unhandled exception in fix_requests_table: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
