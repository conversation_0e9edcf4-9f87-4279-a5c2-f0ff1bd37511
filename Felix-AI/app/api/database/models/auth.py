# pylint: skip-file
"""Authentication Model."""
from typing import Optional
import os
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Check if we should use mock authentication
USE_MOCK_AUTH = os.getenv("USE_MOCK_AUTH", "true").lower() == "true"

try:
    from fastapi import HTTPException
    from fastapi.openapi.models import (
        OAuthFlows as OAuthFlowsModel,
        SecurityBase as SecurityBaseModel,
    )
    from fastapi.security import OAuth2
    from fastapi.security.base import SecurityBase
    from fastapi.security.utils import get_authorization_scheme_param
    from starlette.requests import Request
    from starlette.status import HTTP_403_FORBIDDEN

    if USE_MOCK_AUTH:
        # Use mock authentication
        logger.info("Using mock authentication")
        from app.api.database.models.mock_auth import (
            azure_scheme,
            oauth2_scheme,
            pwd_context,
        )
    else:
        # Use real authentication
        logger.info("Using real authentication")
        from passlib.context import CryptContext
        from fastapi_azure_auth import MultiTenantAzureAuthorizationCodeBearer
except ImportError as e:
    logger.error(f"Error importing authentication dependencies: {e}")
    logger.info("Falling back to mock authentication")
    USE_MOCK_AUTH = True
    from app.api.database.models.mock_auth import (
        azure_scheme,
        oauth2_scheme,
        pwd_context,
    )


class OAuth2PasswordBearerCookie(OAuth2):
    """Authentication Bearer Cookie."""

    def __init__(
        self,
        token_url: str,
        scheme_name: str = None,
        scopes: dict = None,
        auto_error: bool = True,
    ) -> None:
        if not scopes:
            scopes = {}
        flows = OAuthFlowsModel(password={"tokenUrl": token_url, "scopes": scopes})
        super().__init__(flows=flows, scheme_name=scheme_name, auto_error=auto_error)

    async def __call__(self, request: Request) -> Optional[str]:
        header_authorization: str = request.headers.get("Authorization")
        cookie_authorization: str = request.cookies.get("Authorization")

        header_scheme, header_param = get_authorization_scheme_param(
            header_authorization
        )
        cookie_scheme, cookie_param = get_authorization_scheme_param(
            cookie_authorization
        )

        if header_scheme.lower() == "bearer" and header_param != 'undefined':
            authorization = True
            scheme = header_scheme
            param = header_param
        elif cookie_scheme.lower() == "bearer" and cookie_param != 'undefined':
            authorization = True
            scheme = cookie_scheme
            param = cookie_param
        else:
            authorization = False
            scheme = ""
            param = ""

        if not authorization or scheme.lower() != "bearer":
            if self.auto_error:
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN, detail="Not authenticated"
                )
            return None

        return param


class BasicAuth(SecurityBase):
    """Basic Auth."""

    def __init__(self, scheme_name: str = None, auto_error: bool = True):
        self.model = SecurityBaseModel(type="http")
        self.scheme_name = scheme_name or self.__class__.__name__
        self.auto_error = auto_error

    async def __call__(self, request: Request) -> Optional[str]:
        authorization: str = request.headers.get("Authorization")
        scheme, param = get_authorization_scheme_param(authorization)
        if not authorization or scheme.lower() != "basic":
            if self.auto_error:
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN, detail="Not authenticated"
                )
            return None
        return param


# Create a basic auth instance
basic_auth = BasicAuth(auto_error=False)

# Define authentication components if not using mock
if not USE_MOCK_AUTH:
    # Create a password context
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    # Create an OAuth2 scheme
    oauth2_scheme = OAuth2PasswordBearerCookie(token_url="/token")

    # Create an Azure authentication scheme
    azure_scheme = MultiTenantAzureAuthorizationCodeBearer(
        app_client_id=os.getenv("APP_CLIENT_ID"),
        scopes={
            f'api://{os.getenv("APP_CLIENT_ID")}/user_impersonation': 'user_impersonation',
        },
        validate_iss=False,
        allow_guest_users=True
    )
