"""Mock authentication models for development."""
import logging
from typing import Dict, List, Optional, Any

from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, Request, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Configure logging
logger = logging.getLogger(__name__)

class MockOpenIDConfig:
    """Mock OpenID configuration."""
    
    def __init__(self):
        self.config = {
            "issuer": "https://login.microsoftonline.com/mock-tenant-id/v2.0",
            "authorization_endpoint": "https://login.microsoftonline.com/mock-tenant-id/oauth2/v2.0/authorize",
            "token_endpoint": "https://login.microsoftonline.com/mock-tenant-id/oauth2/v2.0/token",
            "jwks_uri": "https://login.microsoftonline.com/mock-tenant-id/discovery/v2.0/keys",
        }
    
    async def load_config(self):
        """Load OpenID configuration."""
        logger.info("Loading mock OpenID configuration")
        return self.config

class MockAzureAuthorizationCodeBearer(HTTPBearer):
    """Mock Azure authorization code bearer."""
    
    def __init__(
        self,
        tenant_id: str = "mock-tenant-id",
        app_client_id: str = "mock-client-id",
        scopes: Optional[List[str]] = None,
        auto_error: bool = True,
    ):
        super().__init__(auto_error=auto_error)
        self.tenant_id = tenant_id
        self.app_client_id = app_client_id
        self.scopes = scopes or []
        self.openid_config = MockOpenIDConfig()
    
    async def __call__(self, request: Request) -> Dict[str, Any]:
        """Validate the token."""
        try:
            # Try to get the credentials
            credentials: HTTPAuthorizationCredentials = await super().__call__(request)
            
            # In development mode, we'll accept any token
            logger.info("Using mock authentication - accepting any token")
            return {
                "sub": "mock-user-id",
                "name": "Mock User",
                "email": "<EMAIL>",
                "roles": ["User"],
            }
        except HTTPException:
            # If no token is provided, create a mock user
            if self.auto_error:
                raise
            logger.info("No token provided, using default mock user")
            return {
                "sub": "default-mock-user-id",
                "name": "Default Mock User",
                "email": "<EMAIL>",
                "roles": ["User"],
            }
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            if self.auto_error:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return {
                "sub": "error-mock-user-id",
                "name": "Error Mock User",
                "email": "<EMAIL>",
                "roles": ["User"],
            }

# Create a mock Azure authentication scheme
azure_scheme = MockAzureAuthorizationCodeBearer()

# Create a mock OAuth2 password bearer
oauth2_scheme = HTTPBearer()

# Create a mock password context
class MockPwdContext:
    """Mock password context."""
    
    def verify(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password."""
        # In development mode, we'll accept any password
        return True
    
    def hash(self, password: str) -> str:
        """Hash password."""
        # In development mode, we'll return a fixed hash
        return "mock-hashed-password"

# Create a mock password context
pwd_context = MockPwdContext()
