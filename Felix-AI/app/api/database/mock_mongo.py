"""Mock MongoDB connection for compatibility."""
import logging
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)

class MockCollection:
    """Mock MongoDB collection."""
    
    def __init__(self, name: str):
        self.name = name
        self._data = []
        logger.info(f"Created mock MongoDB collection: {name}")
    
    async def find_one(self, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find one document in the collection."""
        logger.info(f"Mock find_one in {self.name}: {query}")
        for doc in self._data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                return doc
        return None
    
    async def find(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find documents in the collection."""
        logger.info(f"Mock find in {self.name}: {query}")
        results = []
        for doc in self._data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                results.append(doc)
        return results
    
    async def insert_one(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Insert one document into the collection."""
        logger.info(f"Mock insert_one in {self.name}: {document}")
        self._data.append(document)
        return {"inserted_id": document.get("_id", "mock_id")}
    
    async def insert_many(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Any]]:
        """Insert many documents into the collection."""
        logger.info(f"Mock insert_many in {self.name}: {len(documents)} documents")
        for doc in documents:
            self._data.append(doc)
        return {"inserted_ids": [doc.get("_id", "mock_id") for doc in documents]}
    
    async def update_one(self, query: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, int]:
        """Update one document in the collection."""
        logger.info(f"Mock update_one in {self.name}: {query} -> {update}")
        for i, doc in enumerate(self._data):
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                if "$set" in update:
                    for key, value in update["$set"].items():
                        self._data[i][key] = value
                return {"modified_count": 1}
        return {"modified_count": 0}
    
    async def delete_one(self, query: Dict[str, Any]) -> Dict[str, int]:
        """Delete one document from the collection."""
        logger.info(f"Mock delete_one in {self.name}: {query}")
        for i, doc in enumerate(self._data):
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                self._data.pop(i)
                return {"deleted_count": 1}
        return {"deleted_count": 0}

class MockDatabase:
    """Mock MongoDB database."""
    
    def __init__(self, name: str):
        self.name = name
        self._collections = {}
        logger.info(f"Created mock MongoDB database: {name}")
    
    def __getattr__(self, name: str) -> MockCollection:
        """Get a collection by name."""
        if name not in self._collections:
            self._collections[name] = MockCollection(name)
        return self._collections[name]
    
    def get_collection(self, name: str) -> MockCollection:
        """Get a collection by name."""
        if name not in self._collections:
            self._collections[name] = MockCollection(name)
        return self._collections[name]

class MockClient:
    """Mock MongoDB client."""
    
    def __init__(self, uri: str):
        self.uri = uri
        self._databases = {}
        logger.info(f"Created mock MongoDB client: {uri}")
    
    def __getattr__(self, name: str) -> MockDatabase:
        """Get a database by name."""
        if name not in self._databases:
            self._databases[name] = MockDatabase(name)
        return self._databases[name]
    
    def get_database(self, name: str) -> MockDatabase:
        """Get a database by name."""
        if name not in self._databases:
            self._databases[name] = MockDatabase(name)
        return self._databases[name]

# Create mock MongoDB client
mock_client = MockClient("mongodb://localhost:27017")
mock_db = mock_client.felix_ai

# Export collections for compatibility
user_collection = mock_db.users
file_collection = mock_db.files
request_collection = mock_db.requests
