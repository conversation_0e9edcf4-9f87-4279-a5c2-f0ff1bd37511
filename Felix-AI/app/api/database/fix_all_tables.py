"""Fix all tables by ensuring they have the required columns."""
import asyncio
import logging
import sys
import os
from dotenv import load_dotenv
import asyncpg

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)
load_dotenv()

async def fix_all_tables():
    """Fix all tables by ensuring they have the required columns."""
    logger.info("Fixing all tables to ensure they have the required columns...")

    # Parse database URL
    db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@postgres:5432/felix_ai")
    
    # Extract connection parameters from the URL
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "")
    elif "postgresql://" in db_url:
        db_url = db_url.replace("postgresql://", "")
    
    # Parse the URL
    user_pass, host_port_db = db_url.split("@")
    if ":" in user_pass:
        user, password = user_pass.split(":")
    else:
        user = user_pass
        password = ""
    
    host_port, db_name = host_port_db.split("/")
    if ":" in host_port:
        host, port = host_port.split(":")
        port = int(port)
    else:
        host = host_port
        port = 5432

    try:
        # Connect to the database
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db_name
        )
        
        # Define the required columns for each table
        required_columns = {
            'requests': {
                'created_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP",
                'updated_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP"
            },
            'files': {
                'blob_url': "VARCHAR DEFAULT ''",
                'created_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP",
                'updated_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP"
            },
            'users': {
                'created_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP",
                'updated_at': "VARCHAR DEFAULT CURRENT_TIMESTAMP"
            }
        }
        
        # Fix each table
        for table, columns in required_columns.items():
            # Check if the table exists
            table_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = $1
            );
            """
            table_exists = await conn.fetchval(table_exists_query, table)
            
            if not table_exists:
                logger.warning(f"Table {table} does not exist, skipping...")
                continue
            
            # Check which columns exist
            check_query = f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '{table}';
            """
            existing_columns = [col['column_name'] for col in await conn.fetch(check_query)]
            
            # Add missing columns
            for column, definition in columns.items():
                if column not in existing_columns:
                    logger.info(f"Adding {column} column to {table} table...")
                    add_column_query = f"""
                    ALTER TABLE {table} 
                    ADD COLUMN {column} {definition};
                    """
                    await conn.execute(add_column_query)
        
        # Close the connection
        await conn.close()
        logger.info("All tables fixed successfully.")
        return True
    except Exception as e:
        logger.error(f"Error fixing tables: {e}")
        return False

def main():
    """Run the fix."""
    try:
        result = asyncio.run(fix_all_tables())
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"Unhandled exception in fix_all_tables: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
