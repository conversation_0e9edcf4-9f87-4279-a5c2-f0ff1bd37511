"""Fix the files table by directly adding missing columns using SQL."""
import asyncio
import logging
import sys
import os
from dotenv import load_dotenv
import asyncpg

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)
load_dotenv()

async def fix_files_table_direct():
    """Fix the files table by directly adding missing columns using SQL."""
    logger.info("Fixing files table directly with SQL...")

    # Parse database URL
    db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@postgres:5432/felix_ai")
    
    # Extract connection parameters from the URL
    if "postgresql+asyncpg://" in db_url:
        db_url = db_url.replace("postgresql+asyncpg://", "")
    elif "postgresql://" in db_url:
        db_url = db_url.replace("postgresql://", "")
    
    # Parse the URL
    user_pass, host_port_db = db_url.split("@")
    if ":" in user_pass:
        user, password = user_pass.split(":")
    else:
        user = user_pass
        password = ""
    
    host_port, db_name = host_port_db.split("/")
    if ":" in host_port:
        host, port = host_port.split(":")
        port = int(port)
    else:
        host = host_port
        port = 5432

    try:
        # Connect to the database
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db_name
        )
        
        # Check if the columns exist
        check_query = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'files' 
        AND column_name IN ('blob_url', 'created_at', 'updated_at');
        """
        columns = await conn.fetch(check_query)
        existing_columns = [col['column_name'] for col in columns]
        
        # Add missing columns
        if 'blob_url' not in existing_columns:
            logger.info("Adding blob_url column to files table...")
            await conn.execute("""
            ALTER TABLE files 
            ADD COLUMN blob_url VARCHAR DEFAULT '';
            """)
        
        if 'created_at' not in existing_columns:
            logger.info("Adding created_at column to files table...")
            await conn.execute("""
            ALTER TABLE files 
            ADD COLUMN created_at VARCHAR DEFAULT CURRENT_TIMESTAMP;
            """)
        
        if 'updated_at' not in existing_columns:
            logger.info("Adding updated_at column to files table...")
            await conn.execute("""
            ALTER TABLE files 
            ADD COLUMN updated_at VARCHAR DEFAULT CURRENT_TIMESTAMP;
            """)
        
        # Close the connection
        await conn.close()
        logger.info("Files table fixed successfully with direct SQL.")
        return True
    except Exception as e:
        logger.error(f"Error fixing files table with direct SQL: {e}")
        return False

def main():
    """Run the fix."""
    try:
        result = asyncio.run(fix_files_table_direct())
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"Unhandled exception in fix_files_table_direct: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
