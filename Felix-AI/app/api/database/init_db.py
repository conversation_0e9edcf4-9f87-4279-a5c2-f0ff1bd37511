"""Initialize the database with required tables."""
import asyncio
import logging
import sys
from sqlmodel import SQLModel

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

async def init_db():
    """Create all tables in the database."""
    logger.info("Creating database tables...")

    try:
        # Import here to avoid circular imports
        from app.providers.database import async_engine

        # Import models to ensure they're registered with SQLModel
        import app.models

        # Create tables
        async with async_engine.begin() as conn:
            await conn.run_sync(SQLModel.metadata.create_all)

        logger.info("Database tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        # Don't raise the exception, just return False
        return False

def main():
    """Run the database initialization."""
    try:
        result = asyncio.run(init_db())
        sys.exit(0 if result else 1)
    except Exception as e:
        logger.error(f"Unhandled exception in database initialization: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
