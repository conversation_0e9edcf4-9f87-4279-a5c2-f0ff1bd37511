"""Connect Mongo Database."""
import os
import logging

logger = logging.getLogger(__name__)

# Check if we should use real MongoDB or mock
USE_REAL_MONGO = os.getenv("USE_REAL_MONGO", "false").lower() == "true"

try:
    if USE_REAL_MONGO:
        import pymongo
        from app.core.constant import MONGO_DETAILS

        logger.info(f"Connecting to MongoDB at {MONGO_DETAILS}")
        client = pymongo.MongoClient(MONGO_DETAILS, serverSelectionTimeoutMS=5000)
        database = client.test

        user_collection = database.get_collection("user")
        user_information_collection = database.get_collection("user_information")
        file_collection = database.get_collection("file")
        request_collection = database.get_collection("request")

        logger.info("Successfully connected to MongoDB")
    else:
        logger.info("Using mock MongoDB connection")
        from app.api.database.mock_mongo import user_collection, file_collection, request_collection

        # Create mock user_information_collection
        from app.api.database.mock_mongo import mock_db
        user_information_collection = mock_db.user_information

        logger.info("Mock MongoDB connection initialized")
except Exception as e:
    logger.error(f"Error connecting to MongoDB: {e}")
    logger.info("Falling back to mock MongoDB connection")
    from app.api.database.mock_mongo import user_collection, file_collection, request_collection

    # Create mock user_information_collection
    from app.api.database.mock_mongo import mock_db
    user_information_collection = mock_db.user_information

    logger.info("Mock MongoDB connection initialized")
