"""Schemas for the Felix AI backend app."""
from __future__ import annotations

from enum import Enum
from typing import List
from pydantic import BaseModel

class FileTypeEnum(str, Enum):
    """File Type Enum."""
    PDF = "pdf"
    CSV = "csv"

class FileUploadRequest(BaseModel):
    # File Upload Request.
    base64_data: str
    file_name: str
    file_type: FileTypeEnum
    file_id: str

class ProcessRequest(BaseModel):
    request_id: str