import json
import os
import logging
from datetime import datetime, timezone
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Check if we should use mock logger
USE_MOCK_LOGGER = os.getenv("USE_MOCK_LOGGER", "true").lower() == "true"

try:
    if USE_MOCK_LOGGER:
        # Use mock logger
        logger.info("Using mock logger")
        from app.api.services.mock_logger import MockLogger as LoggerClass
    else:
        # Use real logger
        logger.info("Using real logger")
        import aiohttp
        LoggerClass = None  # Will be defined below
except ImportError as e:
    logger.error(f"Error importing logger dependencies: {e}")
    logger.info("Falling back to mock logger")
    USE_MOCK_LOGGER = True
    from app.api.services.mock_logger import MockLogger as LoggerClass

# Define the real Logger class if not using mock
if not USE_MOCK_LOGGER:
    class RealLogger:
        def __init__(self, app_name, url):
            self.app_name = app_name
            self.url = url
            self.request_id = "unknown"   # Default value
            self.API_url = "unknown"     # Default value
            self.method = "unknown"       # Default value

        async def register(self, api_url, method, requestID=None):
            if requestID:
                self.request_id = requestID
            else:
                self.request_id = str(uuid.uuid4())

            self.API_url = api_url
            self.method = method.upper()
            return self.request_id

        def current_time_to_nanoseconds(self):
            """Get the current time in nanoseconds since epoch."""
            current_time_utc = datetime.now(timezone.utc)
            timestamp_ns = int(current_time_utc.timestamp() * 1_000_000_000)
            return timestamp_ns

        def serialize_content(self, content):
            """Convert content to a human-readable format."""
            if isinstance(content, dict):
                return json.dumps(content, default=str)  # Convert dicts to JSON
            elif isinstance(content, list):
                return json.dumps(content, default=str)  # Convert lists to JSON
            elif hasattr(content, '__dict__'):  # If it's an object, get its attributes
                return json.dumps(vars(content), default=str)
            else:
                return str(content)  # Convert other types to string

        async def log(self, level, content, context:str=None):
            """Send a log message to the logging server asynchronously."""

            content = self.serialize_content(content)  # Convert content to readable format
            if context is None:
                context = "other"

            log_payload = {
                "streams": [
                    {
                        "stream": {
                            "app_name": self.app_name,
                            "request_id": self.request_id,
                            "level": level,
                            "api_url": self.API_url,
                            "method": self.method,
                            "context": context
                        },
                        "values": [
                            [str(self.current_time_to_nanoseconds()), content]
                        ]
                    }
                ]
            }
            # Send log request asynchronously
            await self.__send_log_request(log_payload)

        async def error(self, content, context=None):
            """Log an error message asynchronously."""
            try:
                await self.log(level="error", content=content, context=context)
            except Exception as e:
                logger.error(f"Failed to log error: {e}")

        async def print(self, content, context=None):
            """Log a debug message asynchronously."""
            try:
                await self.log(level="debug", content=content, context=context)
            except Exception as e:
                logger.error(f"Failed to log debug: {e}")

        async def warning(self, content, context=None):
            """Log a warning message asynchronously."""
            try:
                await self.log(level="warning", content=content, context=context)
            except Exception as e:
                logger.error(f"Failed to log warning: {e}")

        async def critical(self, content, context=None):
            """Log a critical message asynchronously."""
            try:
                await self.log(level="critical", content=content, context=context)
            except Exception as e:
                logger.error(f"Failed to log critical: {e}")

        async def __send_log_request(self, log_payload):
            """Send the log payload to the logging server asynchronously."""
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(self.url + "/loki/api/v1/push", json=log_payload) as response:
                        response.raise_for_status()  # Ensure that any HTTP errors raise an exception
            except Exception as e:
                logger.error(f"Failed to send log: {str(e)}")

    # Set LoggerClass to RealLogger
    LoggerClass = RealLogger

# Use the appropriate Logger class
class Logger(LoggerClass):
    """Logger class that uses either the real or mock implementation."""
    pass

# Initialize the logger
logger = Logger(app_name="chatbot", url="http://loki:3100")