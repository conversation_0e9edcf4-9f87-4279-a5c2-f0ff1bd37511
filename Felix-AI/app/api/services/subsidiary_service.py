"""Subsidiary Service."""
import time
import uuid

from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.subsidiary import SubsidiaryCreate, SubsidiaryRead, SubsidiaryUpdate, Subsidiary


class SubsidiaryService:
    """Subsidiary Service."""
    @staticmethod
    async def create_subsidiary(
        session: AsyncSession,
        subsidiary_data: SubsidiaryCreate
    ) -> Subsidiary:
        """Create a new subsidiary in the database.

        Args:
            session (AsyncSession): Database session.
            subsidiary_data (SubsidiaryCreate): Subsidiary data to be created.

        Returns:
            Subsidiary: Created subsidiary object.
        """
        # Random create subsidiary id
        subsidiary_dict = subsidiary_data.model_dump(by_alias=True)

        db_subsidiary = Subsidiary.model_validate(subsidiary_dict)
        session.add(db_subsidiary)
        await session.commit()
        await session.refresh(db_subsidiary)
        return db_subsidiary

    @staticmethod
    async def update_subsidiary(
        session: AsyncSession,
        subsidiary_id: uuid.UUID,
        subsidiary_data: SubsidiaryUpdate
    ) -> Subsidiary:
        """ Update an existing subsidiary in the database.

        Args:
            session (AsyncSession): Database session.
            subsidiary_id (uuid.UUID): Subsidiary ID to be updated.
            subsidiary_data (SubsidiaryUpdate): Subsidiary data to be updated.

        Returns:
            Subsidiary: Updated subsidiary object.
        """
        subsidiary = await session.get(Subsidiary, subsidiary_id)
        if not subsidiary:
            raise ValueError("Subsidiary not found")

        for key, value in subsidiary_data.model_dump(exclude_unset=True).items():
            setattr(subsidiary, key, value)
        setattr(subsidiary, "updated_at", time.strftime("%Y-%m-%dT%H:%M:%SZ"))

        await session.commit()
        await session.refresh(subsidiary)
        return subsidiary

    @staticmethod
    async def delete_subsidiary(
        session: AsyncSession,
        subsidiary_id: uuid.UUID
    ) -> None:
        """ Delete a subsidiary from the database.

        Args:
            session (AsyncSession): Database session.
            subsidiary_id (uuid.UUID): Subsidiary ID to be deleted.

        Returns:
            None
        """
        subsidiary = await session.get(Subsidiary, subsidiary_id)
        if not subsidiary:
            raise ValueError("Subsidiary not found")

        await session.delete(subsidiary)
        await session.commit()
