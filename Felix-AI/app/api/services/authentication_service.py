"""Authentication service."""

from __future__ import annotations

import os
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional as Op, Dict, Union, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Check if we should use mock authentication
USE_MOCK_AUTH = os.getenv("USE_MOCK_AUTH", "true").lower() == "true"

try:
    from fastapi import Depends, HTTPException, status
    from jose import JWTError, jwt

    if not USE_MOCK_AUTH:
        # Use real authentication
        logger.info("Using real authentication service")
        from app.api.database.execute.user import user_execute as execute
        from app.api.database.models.auth import oauth2_scheme, pwd_context
        from app.api.database.models.token import TokenData
        from app.api.database.models.user import UserSchema
        from app.core.constant import ALGORITHM, SECRET_KEY
    else:
        # Use mock authentication
        logger.info("Using mock authentication service")
        from app.api.services.mock_auth import (
            get_current_user as mock_get_current_user,
            get_current_active_user as mock_get_current_active_user,
            authenticate_user as mock_authenticate_user,
            create_access_token as mock_create_access_token,
            verify_password as mock_verify_password,
            User as MockUser,
            oauth2_scheme,
            SECRET_KEY,
            ALGORITHM,
        )

        # Define UserSchema to be compatible with MockUser
        class UserSchema:
            def __init__(self, username: str, email: str, disabled: bool = False):
                self.username = username
                self.email = email
                self.disabled = disabled

        # Define TokenData to be compatible with mock_auth
        class TokenData:
            def __init__(self, username: Optional[str] = None):
                self.username = username
except ImportError as e:
    logger.error(f"Error importing authentication dependencies: {e}")
    logger.info("Falling back to mock authentication service")
    USE_MOCK_AUTH = True

    # Import mock authentication
    from app.api.services.mock_auth import (
        get_current_user as mock_get_current_user,
        get_current_active_user as mock_get_current_active_user,
        authenticate_user as mock_authenticate_user,
        create_access_token as mock_create_access_token,
        verify_password as mock_verify_password,
        User as MockUser,
        oauth2_scheme,
        SECRET_KEY,
        ALGORITHM,
    )

    # Define UserSchema to be compatible with MockUser
    class UserSchema:
        def __init__(self, username: str, email: str, disabled: bool = False):
            self.username = username
            self.email = email
            self.disabled = disabled

    # Define TokenData to be compatible with mock_auth
    class TokenData:
        def __init__(self, username: Optional[str] = None):
            self.username = username


async def get_current_user(token: str = Depends(oauth2_scheme)):
    """Check login token of current user."""
    if USE_MOCK_AUTH:
        # Use mock authentication
        return await mock_get_current_user(token)

    # Use real authentication
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError as exc:
        raise credentials_exception from exc

    user = execute.retrieve_data_by_username(username=token_data.username)
    if user is None:
        raise credentials_exception
    return user


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password."""
    if USE_MOCK_AUTH:
        # Use mock authentication
        return mock_verify_password(plain_password, hashed_password)

    # Use real authentication
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Password to hash password"""
    if USE_MOCK_AUTH:
        # For mock, we'll just return a fixed hash
        return "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"

    # Use real authentication
    return pwd_context.hash(password)


def authenticate_user(username: str, password: str):
    """Authenticate user."""
    if USE_MOCK_AUTH:
        # Use mock authentication
        from app.api.services.mock_auth import mock_users_db
        return mock_authenticate_user(mock_users_db, username, password)

    # Use real authentication
    if user := execute.retrieve_data_by_username(username):
        return user if verify_password(password, user.password) else False
    else:
        return False


def create_access_token(data: dict, expires_delta: Op[timedelta] = None) -> str:
    """Create access token."""
    if USE_MOCK_AUTH:
        # Use mock authentication
        return mock_create_access_token(data, expires_delta)

    # Use real authentication
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode["exp"] = expire
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_active_user(current_user = Depends(get_current_user)):
    """Get current active user."""
    if USE_MOCK_AUTH:
        # Use mock authentication
        return await mock_get_current_active_user(current_user)

    # Use real authentication
    return current_user
