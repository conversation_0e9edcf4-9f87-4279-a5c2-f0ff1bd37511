import uuid
import time
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.request import Request<PERSON><PERSON>, RequestRead, RequestUpdate, Request
from app.api.services.utils import notify_status

class RequestService:
    @staticmethod
    async def create_request(
        session: AsyncSession,
        request_data: RequestCreate
    ) -> Request:
        """Create a new request in the database.

        Args:
            session (AsyncSession): Database session.
            request_data (RequestCreate): Request data to be created.

        Returns:
            Request: Created request object.
        """
        # Random create request id
        request_dict = request_data.model_dump(by_alias=True)

        db_request = Request.model_validate(request_dict)
        session.add(db_request)
        await session.commit()
        await session.refresh(db_request)
        return db_request
    
    @staticmethod
    async def update_request(
        session: AsyncSession,
        request_id: uuid.UUID,
        request_data: RequestUpdate
    ) -> Request:
        """Update an existing request in the database.

        Args:
            session (AsyncSession): Database session.
            request_id (uuid.UUID): Request ID to be updated.
            request_data (RequestUpdate): Request data to be updated.

        Returns:
            Request: Updated request object.
        """
        request = await session.get(Request, request_id)
        if not request:
            raise ValueError("Request not found")

        for key, value in request_data.model_dump(exclude_unset=True).items():
            setattr(request, key, value)
        setattr(request, "updated_at", time.strftime("%Y-%m-%dT%H:%M:%SZ"))

        await session.commit()
        await session.refresh(request)
        return request
    
    @staticmethod
    async def delete_request(
        session: AsyncSession,
        request_id: uuid.UUID
    ) -> None:
        """Delete a request from the database.

        Args:
            session (AsyncSession): Database session.
            request_id (uuid.UUID): Request ID to be deleted.

        Returns:
            None
        """
        request = await session.get(Request, request_id)
        if not request:
            raise ValueError("Request not found")

        await session.delete(request)
        await session.commit()

        notify_status(
            request_id=request_id,
            status="DELETED",
        )