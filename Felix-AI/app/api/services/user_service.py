"""User Service."""
import time
import uuid

from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.database.execute.user import user_execute
from app.api.database.models.user import UserSchema
from app.api.services import authentication_service
from app.models.user import UserCreate, UserRead, UserUpdate, User


class UserService:
    """User Service."""
    @staticmethod
    async def create_user(
        session: AsyncSession,
        user_data: UserCreate
    ) -> User:
        """Create a new user in the database.

        Args:
            session (AsyncSession): Database session.
            user_data (UserCreate): User data to be created.

        Returns:
            User: Created user object.
        """
        # Random create user id
        user_dict = user_data.model_dump(by_alias=True)

        db_user = User.model_validate(user_dict)
        session.add(db_user)
        await session.commit()
        await session.refresh(db_user)
        return db_user
    
    @staticmethod
    async def update_user(
        session: AsyncSession,
        user_id: uuid.UUID,
        user_data: UserUpdate
    ) -> User:
        """ Update an existing user in the database.

        Args:
            session (AsyncSession): Database session.
            user_id (uuid.UUID): User ID to be updated.
            user_data (UserUpdate): User data to be updated.

        Returns:
            User: Updated user object.
        """
        user = await session.get(User, user_id)
        if not user:
            raise ValueError("User not found")

        for key, value in user_data.model_dump(exclude_unset=True).items():
            setattr(user, key, value)
        setattr(user, "updated_at", time.strftime("%Y-%m-%dT%H:%M:%SZ"))

        await session.commit()
        await session.refresh(user)
        return user

    @staticmethod
    async def delete_user(
        session: AsyncSession,
        user_id: uuid.UUID
    ) -> None:
        """ Delete a user from the database.

        Args:
            session (AsyncSession): Database session.
            user_id (uuid.UUID): User ID to be deleted.

        Returns:
            None
        """
        user = await session.get(User, user_id)
        if not user:
            raise ValueError("User not found")

        await session.delete(user)
        await session.commit()

    # @staticmethod
    # def add_user(user: UserSchema):
    #     """Add user to database."""
    #     user_password = user.password
    #     user.password = authentication_service.get_password_hash(user_password)
    #     user.created_at = datetime.now()
    #     user.updated_at = datetime.now()

    #     new_user = user_execute.add_data(user)
    #     new_user.password = user_password
    #     return new_user
