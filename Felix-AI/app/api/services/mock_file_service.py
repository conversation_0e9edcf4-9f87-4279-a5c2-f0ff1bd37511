"""Mock file service for development."""
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, BinaryIO

logger = logging.getLogger(__name__)

# Mock storage for files
mock_files = {}

class MockBlobClient:
    """Mock Azure Blob Storage client."""

    def __init__(self, container_name: str, blob_name: str):
        self.container_name = container_name
        self.blob_name = blob_name
        self.url = f"http://localhost:10000/devstoreaccount1/{container_name}/{blob_name}"
        logger.info(f"Created mock blob client for {container_name}/{blob_name}")

    async def upload_blob(self, data: BinaryIO, overwrite: bool = False) -> Dict:
        """Upload blob to storage."""
        logger.info(f"Uploading blob to {self.container_name}/{self.blob_name}")
        # Store the file in memory
        content = data.read()
        mock_files[f"{self.container_name}/{self.blob_name}"] = {
            "content": content,
            "size": len(content),
            "created_at": datetime.now(),
            "content_type": "application/octet-stream"
        }
        return {"etag": str(uuid.uuid4())}

    async def download_blob(self) -> bytes:
        """Download blob from storage."""
        logger.info(f"Downloading blob from {self.container_name}/{self.blob_name}")
        if f"{self.container_name}/{self.blob_name}" in mock_files:
            return mock_files[f"{self.container_name}/{self.blob_name}"]["content"]
        return b""

    async def delete_blob(self) -> None:
        """Delete blob from storage."""
        logger.info(f"Deleting blob from {self.container_name}/{self.blob_name}")
        if f"{self.container_name}/{self.blob_name}" in mock_files:
            del mock_files[f"{self.container_name}/{self.blob_name}"]

class MockBlobServiceClient:
    """Mock Azure Blob Service client."""

    def __init__(self, account_url: str, credential: Optional[str] = None):
        self.account_url = account_url
        self.credential = credential
        logger.info(f"Created mock blob service client for {account_url}")

    @classmethod
    def from_connection_string(cls, conn_str: str, **kwargs):
        """Create a MockBlobServiceClient from a connection string."""
        logger.info(f"Creating mock blob service client from connection string")
        return cls("http://localhost:10000/devstoreaccount1", "mock-credential")

    def get_container_client(self, container_name: str):
        """Get container client."""
        logger.info(f"Getting container client for {container_name}")
        return MockContainerClient(container_name)

    def get_blob_client(self, **kwargs):
        """Get blob client.

        Accepts either positional parameters (container_name, blob_name) or
        named parameters (container=container_name, blob=blob_name).
        """
        container_name = kwargs.get('container', None)
        blob_name = kwargs.get('blob', None)

        # If container and blob are provided as named parameters
        if container_name and blob_name:
            logger.info(f"Getting blob client for {container_name}/{blob_name}")
            return MockBlobClient(container_name, blob_name)

        # For backward compatibility
        if len(kwargs) >= 2:
            container_name = list(kwargs.keys())[0]
            blob_name = list(kwargs.keys())[1]
            logger.info(f"Getting blob client for {container_name}/{blob_name} (legacy)")
            return MockBlobClient(container_name, blob_name)

        logger.error(f"Invalid parameters for get_blob_client: {kwargs}")
        raise ValueError("Invalid parameters for get_blob_client")

class MockContainerClient:
    """Mock Azure Container client."""

    def __init__(self, container_name: str):
        self.container_name = container_name
        logger.info(f"Created mock container client for {container_name}")

    def list_blobs(self) -> List[Dict]:
        """List blobs in container."""
        logger.info(f"Listing blobs in {self.container_name}")
        blobs = []
        for key in mock_files:
            if key.startswith(f"{self.container_name}/"):
                blob_name = key.split("/", 1)[1]
                blobs.append({
                    "name": blob_name,
                    "size": mock_files[key]["size"],
                    "creation_time": mock_files[key]["created_at"]
                })
        return blobs

    def get_blob_client(self, blob: str = None, **kwargs):
        """Get blob client."""
        # Use the named parameter if provided
        blob_name = blob if blob is not None else kwargs.get('blob_name', '')

        if not blob_name:
            # Try to get the first positional parameter
            if kwargs:
                blob_name = list(kwargs.keys())[0]

        logger.info(f"Getting blob client for {self.container_name}/{blob_name}")
        return MockBlobClient(self.container_name, blob_name)

# Export the mock classes
BlobServiceClient = MockBlobServiceClient
ContainerClient = MockContainerClient
BlobClient = MockBlobClient
