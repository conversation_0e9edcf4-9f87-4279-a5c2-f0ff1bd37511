import os
import uuid
import logging
import time

from dotenv import load_dotenv
from sqlmodel import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.file import FileCreate, FileRead, FileUpdate, File
from app.models.request import Request, RequestStatus
from app.api.services.utils import notify_status

# Configure logging
logger = logging.getLogger(__name__)

# Check if we should use mock file service
USE_MOCK_FILE_SERVICE = os.getenv("USE_MOCK_FILE_SERVICE", "true").lower() == "true"
from app.api.services.utils import notify_status

load_dotenv()

try:
    if USE_MOCK_FILE_SERVICE:
        # Use mock file service
        logger.info("Using mock file service")
        from app.api.services.mock_file_service import BlobServiceClient
    else:
        # Use real file service
        logger.info("Using real file service")
        from azure.storage.blob import BlobServiceClient
except ImportError as e:
    logger.error(f"Error importing file service dependencies: {e}")
    logger.info("Falling back to mock file service")
    USE_MOCK_FILE_SERVICE = True
    from app.api.services.mock_file_service import BlobServiceClient

class FileService:
    def __init__(self):
        # Use a mock connection string if real one is not available
        connection_string = os.getenv("BLOB_STORAGE_CONNECTION_STRING", "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;")

        try:
            self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
            logger.info("Successfully created blob service client")
        except Exception as e:
            logger.error(f"Error creating blob service client: {e}")
            # Create a mock client as fallback
            self.blob_service_client = BlobServiceClient("http://localhost:10000/devstoreaccount1", "mock-credential")
            logger.info("Created mock blob service client as fallback")

        self.container_name = os.getenv("BLOB_CONTAINER_NAME", "client-test-a")

        # Create container if it doesn't exist (for mock service)
        if USE_MOCK_FILE_SERVICE:
            logger.info(f"Using mock container: {self.container_name}")

    async def upload_file(self, session: AsyncSession, temp_file_path: str, file_name: str, file_type: str, file_id: str):
        """Upload a file to Azure Blob Storage."""
        try:
            blob_client = self.blob_service_client.get_blob_client(container=self.container_name, blob=f"{file_name}.{file_type}")
            with open(temp_file_path, "rb") as data:
                # get the file URL to download later
                url = blob_client.url
                blob_client.upload_blob(data, overwrite=True)

            os.remove(temp_file_path)

            file = await session.get(File, file_id)
            if not file:
                raise ValueError("File not found")

            request_id = file.request_id

            # Update the database with the file information
            request = await session.get(Request, request_id)
            
            if not request:
                raise ValueError("Request not found")
            
            setattr(file, "blob_url", url)
            setattr(request, "status", RequestStatus.READY_TO_PROCESS)

            await session.commit()
            await session.refresh(request)
            await session.refresh(file)

            notify_status(
                request_id=request_id,
                status="READY_TO_PROCESS",
            )

            return {"file_id": file.id, "uploading_status": "success"}
        except Exception as e:
            print(f"Error uploading file: {e}")
            return {"file_id": file_id, "uploading_status": "failed"}

    @staticmethod
    async def create_file(
        session: AsyncSession,
        file_data: FileCreate
    ) -> File:
        """Create a new file in the database.

        Args:
            session (AsyncSession): Database session.
            file_data (FileCreate): File data to be created.

        Returns:
            File: Created file object.
        """
        # Random create request id
        file_dict = file_data.model_dump(by_alias=True)

        db_request = File.model_validate(file_dict)
        session.add(db_request)
        await session.commit()
        await session.refresh(db_request)
        return db_request

    @staticmethod
    async def update_file(
        session: AsyncSession,
        file_id: uuid.UUID,
        file_data: FileUpdate
    ) -> File:
        """Update an existing file in the database.

        Args:
            session (AsyncSession): Database session.
            file_id (uuid.UUID): File ID to be updated.
            file_data (FileUpdate): File data to be updated.

        Returns:
            File: Updated file object.
        """
        file = await session.get(File, file_id)
        if not file:
            raise ValueError("File not found")

        for key, value in file_data.model_dump(exclude_unset=True).items():
            setattr(file, key, value)
        setattr(file, "updated_at", time.strftime("%Y-%m-%dT%H:%M:%SZ"))

        await session.commit()
        await session.refresh(file)
        return file

    @staticmethod
    async def delete_file(
        session: AsyncSession,
        file_id: uuid.UUID
    ) -> None:
        """Delete a file from the database.

        Args:
            session (AsyncSession): Database session.
            file_id (uuid.UUID): File ID to be deleted.

        Returns:
            None
        """
        file = await session.get(File, file_id)
        if not file:
            raise ValueError("File not found")

        await session.delete(file)
        await session.commit()