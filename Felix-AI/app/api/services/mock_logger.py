"""Mock logger service for development."""
import json
import logging
from datetime import datetime, timezone
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class MockLogger:
    """Mock logger for development."""
    
    def __init__(self, app_name, url):
        self.app_name = app_name
        self.url = url
        self.request_id = "unknown"
        self.API_url = "unknown"
        self.method = "unknown"
        logger.info(f"Created mock logger for {app_name} at {url}")
    
    async def register(self, api_url, method, requestID=None):
        """Register a new request."""
        if requestID:
            self.request_id = requestID
        else:
            self.request_id = str(uuid.uuid4())
        
        self.API_url = api_url
        self.method = method.upper()
        logger.info(f"Registered request {self.request_id} for {self.API_url} with method {self.method}")
        return self.request_id
    
    def current_time_to_nanoseconds(self):
        """Get the current time in nanoseconds since epoch."""
        current_time_utc = datetime.now(timezone.utc)
        timestamp_ns = int(current_time_utc.timestamp() * 1_000_000_000)
        return timestamp_ns
    
    def serialize_content(self, content):
        """Convert content to a human-readable format."""
        if isinstance(content, dict):
            return json.dumps(content, default=str)  # Convert dicts to JSON
        elif isinstance(content, list):
            return json.dumps(content, default=str)  # Convert lists to JSON
        elif hasattr(content, '__dict__'):  # If it's an object, get its attributes
            return json.dumps(vars(content), default=str)
        else:
            return str(content)  # Convert other types to string
    
    async def log(self, level, content, context=None):
        """Send a log message to the logging server asynchronously."""
        content_str = self.serialize_content(content)
        if context is None:
            context = "other"
        
        log_message = f"[{level.upper()}] [{self.app_name}] [{self.request_id}] [{self.API_url}] [{self.method}] [{context}] {content_str}"
        
        if level == "error":
            logger.error(log_message)
        elif level == "warning":
            logger.warning(log_message)
        elif level == "critical":
            logger.critical(log_message)
        else:
            logger.info(log_message)
    
    async def error(self, content, context=None):
        """Log an error message asynchronously."""
        try:
            await self.log(level="error", content=content, context=context)
        except Exception as e:
            logger.error(f"Failed to log error: {e}")
    
    async def print(self, content, context=None):
        """Log a debug message asynchronously."""
        try:
            await self.log(level="debug", content=content, context=context)
        except Exception as e:
            logger.error(f"Failed to log debug: {e}")
    
    async def warning(self, content, context=None):
        """Log a warning message asynchronously."""
        try:
            await self.log(level="warning", content=content, context=context)
        except Exception as e:
            logger.error(f"Failed to log warning: {e}")
    
    async def critical(self, content, context=None):
        """Log a critical message asynchronously."""
        try:
            await self.log(level="critical", content=content, context=context)
        except Exception as e:
            logger.error(f"Failed to log critical: {e}")
    
    async def __send_log_request(self, log_payload):
        """Mock sending the log payload to the logging server."""
        # In the mock version, we just log the payload locally
        logger.debug(f"Mock log payload: {json.dumps(log_payload)}")

# Initialize the mock logger
mock_logger = MockLogger(app_name="chatbot", url="http://loki:3100")
