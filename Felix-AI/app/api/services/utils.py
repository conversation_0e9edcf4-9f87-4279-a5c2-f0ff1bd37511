import json
import re
import base64
from PIL import Image
from io import BytesIO

status_store = {}  # {request_id: status}
subscribers = {}   # {request_id: [queue1, queue2, ...]}

def notify_status(request_id, status):
    status_store[request_id] = status
    for queue in subscribers.get(request_id, []):
        queue.put_nowait(status)

def json_extractor_with_regex(str_input):
    pattern = "```json(.*?)```"
    match = re.findall(pattern, str_input, re.DOTALL)
    if match:
        json_str = match[0].strip()
        try:
            json2dict = json.loads(json_str)
            return json2dict
        except Exception as e:
            return {}
    else:
        return {}
    
def base64_to_pil_image(base64_str: str) -> Image.Image:
    """Converts a base64 image string into a PIL Image object.

    Args:
        base64_str: The base64 string representing the image.

    Returns:
        The PIL Image object.
    """

    try:
        decoded_bytes = base64.b64decode(base64_str)
        # Create a BytesIO object from the bytes
        image_data = BytesIO(decoded_bytes)
        # Open the image using Pillow (PIL)
        img = Image.open(image_data)
        return img
    except Exception as e:
        print(f"Error decoding base64 image: {e}")
        return None
    