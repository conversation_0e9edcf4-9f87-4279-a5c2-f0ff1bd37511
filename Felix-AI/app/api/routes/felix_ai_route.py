"""AI Flow route."""
import base64
import os
import asyncio
from asyncer import syncify
from dotenv import load_dotenv

from fastapi import APIRouter, Body, HTTPException, Security, Depends
from concurrent.futures import ProcessPoolExecutor
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi_azure_auth.user import User
from asyncer import syncify

from app.api.deps import get_async_session
from app.api.services.logger import Logger
from app.api.responses.base import BaseResponse
from app.api.requests.request import FileUploadRequest, ProcessRequest
from app.api.database.models.auth import azure_scheme
from app.api.services import file_service
from app.ml.base_model import BaseProcess, Case1Process
from app.api.services.utils import notify_status

load_dotenv()

route = APIRouter()
TIMEOUT = 600
FILE_CHUNKING_SIZE = 70
logger = Logger(app_name="Felix-AI", url=os.environ.get("LOGGER_URL"))


@route.get("/health", description="Health check")
async def _function():
    return {"status": "ok"}

@route.post("/upload", description = "Upload file to Azure Blob Storage", dependencies=[Security(azure_scheme)])
async def _upload_file(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    file: FileUploadRequest = Body(...),
) -> BaseResponse:
    """
    Upload file to Azure Blob Storage.

    Args:
        file (FileUploadRequest): File upload request.

    Returns:
        BaseResponse: Response object.
    """
    try:
        # Decode the base64 data
        decoded_data = base64.b64decode(file.base64_data)
        file_name = file.file_name
        # get only the actual extension, ignore enum part
        file_type = file.file_type.value.split(".")[-1]
        file_id = file.file_id

        # Check if file_name already has the extension
        if file_name.lower().endswith(f".{file_type.lower()}"):
            # Remove the extension from file_name to avoid duplication
            file_name = file_name[:-len(f".{file_type}")-1]

        # Create resources directory if it doesn't exist
        os.makedirs("app/resources", exist_ok=True)

        # Generate a unique filename to avoid conflicts
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        safe_file_name = file_name.replace(" ", "_")

        # Save the decoded data to a temporary file
        temp_file_path = f"app/resources/{safe_file_name}_{unique_id}.{file_type}"
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(decoded_data)

        logger.info(f"Saved temporary file to {temp_file_path}")

        # Upload the file to Azure Blob Storage
        result = await file_service.upload_file(session, temp_file_path, file_name, file_type, file_id)

        # Upload the file to Azure Blob Storage
        result = await file_service.upload_file(session, temp_file_path, file_name, file_type, file_id)

        return BaseResponse.success_response(data=result)
    except Exception as e:
        print(e)
        notify_status(
            request_id=file_id,
            status="FAILED",
        )
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.post("/process", description="Process a request", dependencies=[Security(azure_scheme)])
def _process_request(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    request_data: ProcessRequest = Body(...),
) -> BaseResponse:
    """
    Process a request.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        request_data (ProcessRequest): Request data to be processed.

    Returns:
        BaseResponse: Response object.
    """
    try:
        # Get the request ID and process the request
        request_id = request_data.request_id

        # Process the file using the BaseProcess class
        # process = BaseProcess(session, request_id)
        process = Case1Process(session, request_id)
        # result = await process.run()
        result = syncify(process.run)()

        return BaseResponse.success_response(message="Request processed successfully", data=result)
    except Exception as e:
        print(e)
        # Notify the status of the request
        notify_status(
            request_id=request_data.request_id,
            status="FAILED",
        )
        # logger.error(f"Error processing request: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")