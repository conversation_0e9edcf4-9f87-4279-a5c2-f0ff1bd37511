"""File route."""
import os
from fastapi import APIR<PERSON><PERSON>, Body, HTTPException, Security, Depends
from fastapi_azure_auth.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.api.deps import get_async_session
from app.libs.paginate import cursor_page, CommonPage
from app.models.file import File, FileCreate, FileUpdate, FileRead
from app.api.responses.base import BaseResponse
from app.api.services.logger import Logger
from app.api.responses.model2reponse import convert_user_model_to_response
from app.api.database.models.auth import azure_scheme
from app.api.services import file_service

route = APIRouter()
TIMEOUT = 450
# logger = Logger(app_name="Felix-AI", url=os.environ.get("LOGGER_URL"))

@route.post("", response_description="file data added into the database")
async def add_file_data(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    file_data: FileCreate = Body(...),
) -> BaseResponse:
    """
    Create a new file in the SQL database

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        file_data (FileCreate): File data to be created.

    Returns:
        BaseResponse: Response object.
    """
    try:
        db_file = await file_service.create_file(session, file_data)
        return BaseResponse.success_response(message="File created successfully", data=db_file.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error creating file: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("", response_description="files retrieved")
async def get_files(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> CommonPage[File]:  # Updated return type to CommonPage[File]
    """
    Get all files

    Args:
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        CommonPage[File]: Paginated response object.
    """
    try:
        statement = select(File)
        return await cursor_page(statement, session)
    except Exception as e:
        # logger.error(f"Error retrieving files: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("/{file_id}", response_description="file data retrieved")
async def get_file_data(
    file_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Get a file by ID

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        file_id (int): ID of the file to be retrieved.

    Returns:
        BaseResponse: Response object.
    """
    try:
        statement = select(File).where(File.id == file_id)
        db_file = await session.execute(statement)
        db_file = db_file.scalars().first()
        if not db_file:
            raise HTTPException(status_code=404, detail="File not found")
        return BaseResponse.success_response(message="File retrieved successfully", data=db_file.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error retrieving file: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.put("/{file_id}", response_description="file data updated")
async def update_file_data(
    file_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    file_data: FileUpdate = Body(...),
) -> BaseResponse:
    """
    Update a file in the SQL database

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        file_id (str): ID of the file to be updated.
        file_data (FileUpdate): File data to be updated.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await file_service.update_file(session, file_id, file_data)
        return BaseResponse.success_response(message="File updated successfully", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error updating file: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
    
@route.delete("/{file_id}", response_description="file data deleted")
async def delete_file_data(
    file_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Delete a file from the SQL database

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        file_id (int): ID of the file to be deleted.

    Returns:
        BaseResponse: Response object.
    """
    try:
        await file_service.delete_file(session, file_id)
        return BaseResponse.success_response(message="File deleted successfully")
    except Exception as e:
        # logger.error(f"Error deleting file: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")