"""API Routes."""
from fastapi import APIRouter

from app.api.routes import felix_ai_route, user_route, request_route, file_route, subsidiary_route

app = APIRouter()

app.include_router(user_route.route, tags=["User"], prefix="/admin/user")
app.include_router(request_route.route, tags=["Request"], prefix="/request")
app.include_router(file_route.route, tags=["File"], prefix="/file")
app.include_router(subsidiary_route.route, tags=["Subsidiary"], prefix="/subsidiary")
app.include_router(felix_ai_route.route, tags=["Felix AI Process"], prefix="/felix-ai-api")