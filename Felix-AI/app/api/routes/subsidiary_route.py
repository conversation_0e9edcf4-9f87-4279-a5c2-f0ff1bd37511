"""Subsidiary route."""
import os
from fastapi import APIRout<PERSON>, Body, HTTPException, Security, Depends
from fastapi_azure_auth.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.api.deps import get_async_session
from app.libs.paginate import cursor_page, CommonPage
from app.models.subsidiary import SubsidiaryCreate, SubsidiaryUpdate, SubsidiaryRead, Subsidiary
from app.api.responses.base import BaseResponse
from app.api.database.models.auth import azure_scheme
from app.api.services import subsidiary_service

route = APIRouter()
TIMEOUT = 450
# logger = Logger(app_name="Felix-AI", url=os.environ.get("LOGGER_URL"))

@route.post("", response_description="subsidiary data added into the database")
async def add_subsidiary_data(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    subsidiary_data: SubsidiaryCreate = Body(...),
) -> BaseResponse:
    """
    Create a new subsidiary.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        subsidiary_data (SubsidiaryCreate): Subsidiary data to be created.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await subsidiary_service.create_subsidiary(session, subsidiary_data)
        return BaseResponse.success_response(message="Subsidiary created successfully", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error creating subsidiary: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("", response_description="subsidiaries retrieved")
async def get_subsidiaries(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> CommonPage[Subsidiary]:
    """
    Get all subsidiaries.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        CommonPage[SubsidiaryRead]: Paginated response object.
    """
    try:
        statement = select(Subsidiary)
        return await cursor_page(statement, session)
    except Exception as e:
        # logger.error(f"Error retrieving subsidiaries: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("/{subsidiary_id}", response_description="subsidiary data retrieved")
async def get_subsidiary_data(
    subsidiary_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Get subsidiary information by id.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        subsidiary_id (str): Subsidiary ID.

    Returns:
        BaseResponse: Response object.
    """
    try:
        statement = select(Subsidiary).where(Subsidiary.id == subsidiary_id)
        result = await session.execute(statement)
        db_subsidiary = result.scalars().first()
        if db_subsidiary is None:
            raise HTTPException(status_code=404, detail="Subsidiary not found")
        return BaseResponse.success_response(data=db_subsidiary.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error retrieving subsidiary: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.put("/{subsidiary_id}", response_description="subsidiary data updated")
async def update_subsidiary_data(
    subsidiary_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    subsidiary_data: SubsidiaryUpdate = Body(...),
) -> BaseResponse:
    """
    Update subsidiary data.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        subsidiary_id (str): Subsidiary ID.
        subsidiary_data (SubsidiaryUpdate): Subsidiary data to be updated.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await subsidiary_service.update_subsidiary(session, subsidiary_id, subsidiary_data)
        return BaseResponse.success_response(message=f"successful subsidiary update with ID {subsidiary_id}", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error updating subsidiary: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.delete("/{subsidiary_id}", response_description="subsidiary data deleted from the database")
async def delete_subsidiary_data(
    subsidiary_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Delete subsidiary data.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        subsidiary_id (str): Subsidiary ID.

    Returns:
        BaseResponse: Response object.
    """
    try:
        await subsidiary_service.delete_subsidiary(session, subsidiary_id)
        return BaseResponse.success_response(message=f"successful subsidiary delete with ID {subsidiary_id}")
    except Exception as e:
        # logger.error(f"Error deleting subsidiary: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")
