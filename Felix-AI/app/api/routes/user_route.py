"""User route."""
import os
from fastapi import APIRout<PERSON>, Body, HTTPException, Security, Depends
from fastapi_azure_auth.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.api.deps import get_async_session
from app.libs.paginate import cursor_page, CommonPage
from app.models.user import UserCreate, UserUpdate, UserRead, User
from app.api.responses.base import BaseResponse
from app.api.services.logger import Logger
from app.api.responses.model2reponse import convert_user_model_to_response
from app.api.database.models.auth import azure_scheme
from app.api.services import user_service

route = APIRouter()
TIMEOUT = 450
# logger = Logger(app_name="Felix-AI", url=os.environ.get("LOGGER_URL"))

@route.post("", response_description="user data added into the database")
async def add_user_data(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    user_data: UserCreate = Body(...),
) -> BaseResponse:
    """
    Create a new user.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        user_data (UserCreate): User data to be created.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await user_service.create_user(session, user_data)
        return BaseResponse.success_response(message="User created successfully", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("", response_description="users retrieved")
async def get_users(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> CommonPage[User]:
    """
    Get all users.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        CommonPage[UserRead]: Paginated response object.
    """
    try:
        statement = select(User)
        return await cursor_page(statement, session)
    except Exception as e:
        # logger.error(f"Error retrieving users: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("/{user_id}", response_description="user data retrieved")
async def get_user_data(
    user_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Get user information by id.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        user_id (str): User ID.

    Returns:
        BaseResponse: Response object.
    """
    try:
        statement = select(User).where(User.id == user_id)
        result = await session.execute(statement)
        db_user = result.scalars().first()
        if db_user is None:
            raise HTTPException(status_code=404, detail="User not found")   
        return BaseResponse.success_response(data=db_user.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error retrieving user: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")
    
@route.put("/{user_id}", response_description="user data updated")
async def update_user_data(
    user_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    user_data: UserUpdate = Body(...),
) -> BaseResponse:
    """
    Update user data.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        user_id (str): User ID.
        user_data (UserUpdate): User data to be updated.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await user_service.update_user(session, user_id, user_data)
        return BaseResponse.success_response(message=f"successful user update with ID {user_id}", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error updating user: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.delete("/{user_id}", response_description="user data deleted from the database")
async def delete_user_data(
    user_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Delete user data.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        user_id (str): User ID.

    Returns:
        BaseResponse: Response object.
    """
    try:
        await user_service.delete_user(session, user_id)
        return BaseResponse.success_response(message=f"successful user delete with ID {user_id}")
    except Exception as e:
        # logger.error(f"Error deleting user: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")
