"""Request route."""
import base64
import os
import json
import asyncio
from asyncer import syncify
from dotenv import load_dotenv

import fastapi
from fastapi import APIRouter, Body, HTTPException, Security, Depends
from fastapi_azure_auth.user import User
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from app.api.deps import get_async_session
from app.libs.paginate import cursor_page, CommonPage
from app.models.request import RequestCreate, RequestUpdate, RequestRead, Request
from app.api.services.logger import Logger
from app.api.responses.base import BaseResponse
from app.api.requests.request import FileUploadRequest
from app.api.database.models.auth import azure_scheme
from app.api.services import request_service
from app.api.services.utils import subscribers, status_store

load_dotenv()

route = APIRouter()
TIMEOUT = 450
# logger = Logger(app_name="Felix-AI", url=os.environ.get("LOGGER_URL"))

@route.get("", description="Get all requests", dependencies=[Security(azure_scheme)])
async def get_requests(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session)
) -> CommonPage[Request]:
    """
    Get all requests.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        CommonPage[Request]: Paginated response object.
    """
    try:
        statement = select(Request)
        return await cursor_page(statement, session)
    except Exception as e:
        print(f"Error retrieving requests: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.post("", description="Create a new request", dependencies=[Security(azure_scheme)])
async def create_request(
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    request_data: RequestCreate = Body(...),
) -> BaseResponse:
    """
    Create a new request.

    Args:
        user (User): User object.
        session (AsyncSession): Database session.
        request_data (RequestCreate): Request data to be created.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await request_service.create_request(session, request_data)
        return BaseResponse.success_response(message="Request created successfully", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error creating request: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.get("/{request_id}", description="Get request by ID", dependencies=[Security(azure_scheme)])
async def get_request_by_id(
    request_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Get request by ID.

    Args:
        request_id (str): Request ID.
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        BaseResponse: Response object.
    """
    try:
        statement = select(Request).where(Request.id == request_id)
        result = await session.execute(statement)
        db_request = result.scalar_one_or_none()
        if db_request is None:
            raise HTTPException(status_code=404, detail="Request not found")
        return BaseResponse.success_response(data=db_request.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error retrieving request: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")

@route.put("/{request_id}", description="Update request by ID", dependencies=[Security(azure_scheme)])
async def update_request(
    request_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
    request_data: RequestUpdate = Body(...),
) -> BaseResponse:
    """
    Update request by ID.

    Args:
        request_id (str): Request ID.
        user (User): User object.
        session (AsyncSession): Database session.
        request_data (RequestUpdate): Request data to be updated.

    Returns:
        BaseResponse: Response object.
    """
    try:
        result = await request_service.update_request(session, request_id, request_data)
        return BaseResponse.success_response(message="Request updated successfully", data=result.model_dump(by_alias=True))
    except Exception as e:
        # logger.error(f"Error updating request: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")
    
@route.delete("/{request_id}", description="Delete request by ID", dependencies=[Security(azure_scheme)])
async def delete_request(
    request_id: str,
    user: User = Depends(azure_scheme),
    session: AsyncSession = Depends(get_async_session),
) -> BaseResponse:
    """
    Delete request by ID.

    Args:
        request_id (str): Request ID.
        user (User): User object.
        session (AsyncSession): Database session.

    Returns:
        BaseResponse: Response object.
    """
    try:
        await request_service.delete_request(session, request_id)
        return BaseResponse.success_response(message="Request deleted successfully")
    except Exception as e:
        # logger.error(f"Error deleting request: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail="Internal Server Error")
    
@route.get("/status/stream/{request_id}")
async def status_stream(request_id: str, request: fastapi.Request):
    async def event_generator():
        queue = asyncio.Queue()
        if request_id not in subscribers:
            subscribers[request_id] = []
        subscribers[request_id].append(queue)
        try:
            while True:
                # Wait for a status update or client disconnect
                try:
                    status = await asyncio.wait_for(queue.get(), timeout=30)
                    yield f"data: {json.dumps({'status': status})}\n\n"
                except asyncio.TimeoutError:
                    status = None
                    pass
                    # Send a comment (keep-alive) every 30s
                    yield ":\n\n"
                
                if await request.is_disconnected():
                    break
                if status == "DELETED":
                    break
        finally:
            subscribers[request_id].remove(queue)
            if not subscribers[request_id]:  # If list is empty
                subscribers.pop(request_id)
    return StreamingResponse(event_generator(), media_type="text/event-stream")