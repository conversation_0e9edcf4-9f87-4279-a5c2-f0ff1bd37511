"""Define a config for project."""
from __future__ import annotations

import logging
import os
import sys

from dotenv import load_dotenv
from starlette.config import Config
from starlette.datastructures import CommaSeparatedStrings

# Check if we should use mock loguru
USE_MOCK_LOGURU = os.getenv("USE_MOCK_LOGURU", "true").lower() == "true"

try:
    if USE_MOCK_LOGURU:
        # Use mock loguru
        logging.info("Using mock loguru")
        from app.core.mock_loguru import logger
    else:
        # Use real loguru
        logging.info("Using real loguru")
        from loguru import logger
except ImportError as e:
    logging.error(f"Error importing loguru: {e}")
    logging.info("Falling back to mock loguru")
    from app.core.mock_loguru import logger

# Import InterceptHandler after logger is defined
from app.core.logging import InterceptHandler

load_dotenv()

# Ensure API_PREFIX starts with a slash
API_PREFIX = "/api/v1"

VERSION = "0.0.0"

config = Config()

DEBUG: bool = config("DEBUG", cast=bool, default=False)

PROJECT_NAME: str = config("Felix AI",
                           default="Felix AI application")
ALLOWED_HOSTS = config(
    "ALLOWED_HOSTS",
    cast=CommaSeparatedStrings,
    default="",
)

# logging configuration

LOGGING_LEVEL = logging.DEBUG if DEBUG else logging.INFO
LOGGERS = ("uvicorn.asgi", "uvicorn.access")

logging.getLogger().handlers = [InterceptHandler()]
for logger_name in LOGGERS:
    logging_logger = logging.getLogger(logger_name)
    logging_logger.handlers = [InterceptHandler(level=LOGGING_LEVEL)]

logger.configure(handlers=[{"sink": sys.stderr, "level": LOGGING_LEVEL}])
