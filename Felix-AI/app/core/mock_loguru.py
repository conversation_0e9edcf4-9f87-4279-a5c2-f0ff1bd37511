"""Mock loguru module for development."""
import logging
import sys
from typing import Any, Callable, Dict, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Create a logger
_logger = logging.getLogger("mock_loguru")

class MockLogger:
    """Mock loguru logger."""

    def __init__(self):
        self.handlers = []

    def add(self, sink, **kwargs):
        """Add a handler to the logger."""
        self.handlers.append(sink)
        return self

    def remove(self, handler_id=None):
        """Remove a handler from the logger."""
        if handler_id is not None and handler_id < len(self.handlers):
            self.handlers.pop(handler_id)
        return self

    def configure(self, **kwargs):
        """Configure the logger."""
        return self

    def bind(self, **kwargs):
        """Bind context variables to the logger."""
        return self

    def opt(self, **kwargs):
        """Set options for the logger."""
        return self

    def patch(self, patcher):
        """Patch the logger."""
        return self

    def level(self, name, no=None, color=None, icon=None):
        """Add a logging level."""
        class Level:
            def __init__(self, name):
                self.name = name
        return Level(name)

    def disable(self, name):
        """Disable a logger."""
        pass

    def enable(self, name):
        """Enable a logger."""
        pass

    def debug(self, message, *args, **kwargs):
        """Log a debug message."""
        _logger.debug(message, *args, **kwargs)

    def info(self, message, *args, **kwargs):
        """Log an info message."""
        _logger.info(message, *args, **kwargs)

    def warning(self, message, *args, **kwargs):
        """Log a warning message."""
        _logger.warning(message, *args, **kwargs)

    def error(self, message, *args, **kwargs):
        """Log an error message."""
        _logger.error(message, *args, **kwargs)

    def critical(self, message, *args, **kwargs):
        """Log a critical message."""
        _logger.critical(message, *args, **kwargs)

    def exception(self, message, *args, **kwargs):
        """Log an exception message."""
        _logger.exception(message, *args, **kwargs)

    def log(self, level, message, *args, **kwargs):
        """Log a message with the specified level."""
        if isinstance(level, str):
            level_num = getattr(logging, level.upper(), logging.INFO)
        else:
            level_num = level
        _logger.log(level_num, message, *args, **kwargs)

    def trace(self, message, *args, **kwargs):
        """Log a trace message."""
        _logger.debug(f"TRACE: {message}", *args, **kwargs)

    def success(self, message, *args, **kwargs):
        """Log a success message."""
        _logger.info(f"SUCCESS: {message}", *args, **kwargs)

# Create a mock logger instance
logger = MockLogger()
