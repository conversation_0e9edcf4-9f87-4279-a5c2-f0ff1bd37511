"""Logging."""

import logging
import os
from types import FrameType
from typing import cast

# Check if we should use mock loguru
USE_MOCK_LOGURU = os.getenv("USE_MOCK_LOGURU", "true").lower() == "true"

try:
    if USE_MOCK_LOGURU:
        # Use mock loguru
        from app.core.mock_loguru import logger
    else:
        # Use real loguru
        from loguru import logger
except ImportError:
    # Fallback to mock loguru
    from app.core.mock_loguru import logger


class InterceptHandler(logging.Handler):
    """Intercept Handler."""

    def emit(self, record: logging.LogRecord) -> None:  # pragma: no cover
        """Emit."""
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:  # noqa: WPS609
            frame = cast(FrameType, frame.f_back)
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level,
            record.getMessage(),
        )
