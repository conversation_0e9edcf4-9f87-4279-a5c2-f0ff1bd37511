[tool.poetry]
name = "app"
version = "0.0.1"
description = ""
authors = ["CuongNQ"]
license = "CuongNQ"

[tool.poetry.dependencies]
python = "^3.11"
pylint = "^3.0.2"
python-slugify = "^8.0.1"
Unidecode = "^1.3.7"
loguru = "^0.7.2"
pymongo = { extras = ["srv"], version = "^4.5.0" }
aiofiles = "^23.2.1"
PyYAML = "^6.0.1"
python-dotenv = "^1.0.0"
wget = "^3.2"
passlib = { extras = ["bcrypt"], version = "^1.7.4" }
python-jose = { extras = ["cryptography"], version = "^3.3.0" }
Jinja2 = "^3.1.2"
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.1"
pytest-env = "^1.1.0"
httpx = "^0.25.0"
orjson = "^3.9.10"
aiohttp = "^3.8.6"
pytest-sugar = "^0.9.7"
hypercorn = { extras = ["trio"], version = "^0.15.0" }
gunicorn = "^21.2.0"
python-multipart = "^0.0.6"
fastapi = "0.110.0"
uvicorn = "^0.23.2"
sqlmodel = "^0.0.24"
poetry-plugin-up = "^0.7.0"
pydantic = "^2.4.2"
trio = "0.22.0"
pandas = "^2.2.1"
numpy = "^1.26.4"
pymupdf = "^1.24.2"
pypdf = "4.2.0"
langchain = "^0.3.18"
langchain-experimental = "0.3.4"
langchain-community = "^0.3.17"
openai = "1.75.0"
pdf2image = "1.17.0"
asyncer = "0.0.8"
langchain-openai = "^0.3.6"
google-genai = "^1.1.0"
pyodbc = "^5.2.0"
aioodbc = "^0.5.0"
alembic = "^1.14.0"
fastapi-azure-auth = "^5.1.1"
azure-storage-blob = "^12.25.1"
sqlakeyset = "^2.0.1739066250"
fastapi_pagination = "^0.13.1"
litellm = "^1.67.0"
openpyxl = "^3.1.2"
azure-ai-formrecognizer = "^3.3.3"
tabulate = "^0.9.0"
xlsxwriter = "^3.1.2"

[tool.poetry.dev-dependencies]
black = "^23.10.1"
isort = "^5.12.0"
pyflakes = "3.1.0"
flake8 = "6.1.0"
wemake-python-styleguide = "^0.18.0"
autoflake = "2.2.1"
mypy = "^1.6.1"
flake8-fixme = "^1.1.1"
docker = "^6.1.3"
asgi-lifespan = "^2.1.0"
autopep8 = "2.0.4"

[tool.pylint]
line-length = 120

[tool.isort]
profile = "black"
src_paths = ["app", "tests"]
combine_as_imports = true

[tool.pytest.ini_options]
testpaths = "app/tests"
filterwarnings = '''
    ignore::DeprecationWarning
'''
addopts = '''
  --strict-markers
  --tb=short
  --cov=app
  --cov=tests
  --cov-branch
  --cov-report=term-missing
  --cov-report=html
  --cov-report=xml
  --no-cov-on-fail
  --cov-fail-under=70
'''
env = ["SECRET_KEY=secret"]

[build-system]
requires = ["poetry>=1.0"]
build-backend = "poetry.masonry.api"
