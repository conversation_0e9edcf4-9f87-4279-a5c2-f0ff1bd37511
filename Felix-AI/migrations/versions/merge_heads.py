"""Merge multiple heads

Revision ID: merge_heads
Revises: 001
Create Date: 2023-05-05 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = 'merge_heads'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # This is a merge migration, no schema changes needed
    pass


def downgrade() -> None:
    # This is a merge migration, no schema changes needed
    pass
