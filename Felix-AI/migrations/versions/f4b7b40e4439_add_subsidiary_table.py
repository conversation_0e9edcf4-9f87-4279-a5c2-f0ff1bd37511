"""Add subsidiary table

Revision ID: f4b7b40e4439
Revises: f4acdb15d636
Create Date: 2025-05-22 01:18:09.887130

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'f4b7b40e4439'
down_revision: Union[str, None] = 'f4acdb15d636'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('subsidiaries',
    sa.Column('tenant_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('contact_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('entity_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('entity_code', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sqlmodel.sql.sqltypes.AutoString(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('updated_at', sqlmodel.sql.sqltypes.AutoString(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('subsidiaries')
    # ### end Alembic commands ###
