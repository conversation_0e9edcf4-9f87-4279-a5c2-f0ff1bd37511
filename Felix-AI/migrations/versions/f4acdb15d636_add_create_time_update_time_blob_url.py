"""Add create time, update time, blob url

Revision ID: f4acdb15d636
Revises: a598b4233b58
Create Date: 2025-05-05 21:15:29.524903

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'f4acdb15d636'
down_revision: Union[str, None] = 'a598b4233b58'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('files', sa.Column('blob_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('files', sa.Column('created_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    op.add_column('files', sa.Column('updated_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    op.drop_column('files', 'uploaded_at')
    op.add_column('requests', sa.Column('created_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    op.add_column('requests', sa.Column('updated_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    op.add_column('users', sa.Column('created_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    op.add_column('users', sa.Column('updated_at', sqlmodel.sql.sqltypes.AutoString(), server_default='CURRENT_TIMESTAMP', nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'created_at')
    op.drop_column('requests', 'updated_at')
    op.drop_column('requests', 'created_at')
    op.add_column('files', sa.Column('uploaded_at', sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), autoincrement=False, nullable=True))
    op.drop_column('files', 'updated_at')
    op.drop_column('files', 'created_at')
    op.drop_column('files', 'blob_url')
    # ### end Alembic commands ###
