"""Fix requests table by adding missing columns

Revision ID: fix_requests_table
Revises: f4acdb15d636
Create Date: 2025-05-14 01:45:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'fix_requests_table'
down_revision: Union[str, None] = 'f4acdb15d636'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Check if columns exist before adding them
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('requests')]
    
    # Add created_at column if it doesn't exist
    if 'created_at' not in columns:
        op.add_column('requests', sa.Column('created_at', sqlmodel.sql.sqltypes.AutoString(), 
                                           server_default='CURRENT_TIMESTAMP', nullable=True))
    
    # Add updated_at column if it doesn't exist
    if 'updated_at' not in columns:
        op.add_column('requests', sa.Column('updated_at', sqlmodel.sql.sqltypes.AutoString(), 
                                           server_default='CURRENT_TIMESTAMP', nullable=True))


def downgrade() -> None:
    # Don't drop columns in downgrade to avoid data loss
    pass
