"""Change id to uuid

Revision ID: a598b4233b58
Revises: 6495e327a444
Create Date: 2025-05-02 15:41:30.164044

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'a598b4233b58'
down_revision: Union[str, None] = '6495e327a444'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # Drop constraints manually
    with op.batch_alter_table("files", schema=None) as batch_op:
        batch_op.drop_constraint("PK__files__3213E83F9FAA1BEA", type_="primary")  # replace with actual name or use batch_op.drop_constraint(None, type_="primary")
        # batch_op.drop_constraint("fk_files_request_id_requests", type_="foreignkey")  # adjust name

    with op.batch_alter_table("requests", schema=None) as batch_op:
        batch_op.drop_constraint("PK__requests__3213E83F534E944C", type_="primary")  # replace with actual name

    with op.batch_alter_table("users", schema=None) as batch_op:
        batch_op.drop_constraint("PK__users__3213E83FCAAEEDF2", type_="primary")  # replace with actual name
        
    # Now alter columns
    op.alter_column('files', 'request_id',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=False)
    op.drop_column('files', 'id')

    # Add new UUID column as string
    op.add_column('files', sa.Column('id', sa.String(length=36), nullable=False))
    op.alter_column('requests', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=False)
    op.drop_column('requests', 'id')

    # Add new UUID column as string
    op.add_column('requests', sa.Column('id', sa.String(length=36), nullable=False))
    op.drop_column('users', 'id')

    # Add new UUID column as string
    op.add_column('users', sa.Column('id', sa.String(length=36), nullable=False))

    # Recreate constraints
    with op.batch_alter_table("users", schema=None) as batch_op:
        batch_op.create_primary_key("pk_users", ["id"])

    with op.batch_alter_table("requests", schema=None) as batch_op:
        batch_op.create_primary_key("pk_requests", ["id"])

    with op.batch_alter_table("files", schema=None) as batch_op:
        batch_op.create_primary_key("pk_files", ["id"])
        # batch_op.create_foreign_key("fk_files_request_id_requests", "requests", ["request_id"], ["id"])



def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.Identity(always=False, start=1, increment=1))
    op.alter_column('requests', 'id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.Identity(always=False, start=1, increment=1))
    op.alter_column('requests', 'user_id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('files', 'id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.Identity(always=False, start=1, increment=1))
    op.alter_column('files', 'request_id',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
