"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2023-05-05 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # This will be auto-generated by SQLModel's metadata
    pass


def downgrade() -> None:
    # This will be auto-generated by SQLModel's metadata
    pass
