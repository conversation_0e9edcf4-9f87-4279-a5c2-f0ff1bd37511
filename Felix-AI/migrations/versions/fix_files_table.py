"""Fix files table by adding missing blob_url column

Revision ID: fix_files_table
Revises: fix_requests_table
Create Date: 2025-05-14 01:50:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'fix_files_table'
down_revision: Union[str, None] = 'fix_requests_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Check if column exists before adding it
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('files')]
    
    # Add blob_url column if it doesn't exist
    if 'blob_url' not in columns:
        op.add_column('files', sa.Column('blob_url', sqlmodel.sql.sqltypes.AutoString(), nullable=False, 
                                        server_default=''))


def downgrade() -> None:
    # Don't drop column in downgrade to avoid data loss
    pass
