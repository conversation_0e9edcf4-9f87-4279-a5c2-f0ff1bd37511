import asyncio
from logging.config import fileConfig
from dotenv import load_dotenv
import os

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config
from sqlmodel import SQLModel

from alembic import context

from app.models import *

# load environment variables from .env file
load_dotenv()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = SQLModel.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/felix_ai")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Prioritize DATABASE_URL from docker-compose if available
    docker_compose_db_url = os.getenv("DATABASE_URL")
    db_url = None

    if docker_compose_db_url and 'postgres' in docker_compose_db_url: # Check if it's likely the postgres URL
        print(f"Using DATABASE_URL from environment: {docker_compose_db_url}")
        db_url = docker_compose_db_url
    else:
        # Fallback logic if DATABASE_URL is not set or not postgres (e.g., for Azure)
        # This part retains the original logic for Azure or other setups
        # Ensure load_dotenv() is called if you expect .env to provide Azure details
        # load_dotenv() # Already called globally, consider if local override is needed
        
        azure_sql_username = os.getenv('AZURE_SQL_USERNAME')
        azure_sql_password = os.getenv('AZURE_SQL_PASSWORD')
        azure_sql_host = os.getenv('AZURE_SQL_HOST')
        azure_sql_port = os.getenv('AZURE_SQL_PORT')
        azure_sql_database = os.getenv('AZURE_SQL_DATABASE')

        if all([azure_sql_username, azure_sql_password, azure_sql_host, azure_sql_port, azure_sql_database]):
            print("Using Azure SQL connection details from environment.")
            db_url = f"mssql+aioodbc://{azure_sql_username}:{azure_sql_password}@{azure_sql_host}:{azure_sql_port}/{azure_sql_database}?driver=ODBC+Driver+17+for+SQL+Server&timeout=600"
        elif docker_compose_db_url: # If DATABASE_URL was set but not postgres, use it
             print(f"Using non-Postgres DATABASE_URL from environment: {docker_compose_db_url}")
             db_url = docker_compose_db_url
        else:
            print("Warning: DATABASE_URL not set and Azure SQL connection details are incomplete. Falling back to default offline URL.")
            db_url = "postgresql+asyncpg://postgres:postgres@localhost:5432/felix_ai" # Default fallback

    if not db_url:
        # This case should ideally not be reached if DATABASE_URL is set by docker-compose
        print("Critical Error: db_url is not set. Migrations cannot proceed.")
        # Optionally, raise an exception here
        return


    config_section = config.get_section(config.config_ini_section, {})
    config_section["sqlalchemy.url"] = db_url

    connectable = async_engine_from_config(
        # config.get_section(config.config_ini_section, {}),
        config_section,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""

    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
