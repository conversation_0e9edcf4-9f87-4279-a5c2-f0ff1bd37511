# Felix-AI

This repository contains code for Backend service and AI flow for automating the financial consolidation process.

---

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Installation](#installation)
- [Configuration](#configuration)
- [Database Migrations](#database-migrations)
- [Running the Application](#running-the-application)
- [Testing](#testing)
- [CI/CD](#cicd)
- [Contributing](#contributing)
- [License](#license)

---

## Features

- Async database access with SQLModel and SQLAlchemy
- Database migrations managed with Alembic
- Enum-based status management
- Clean, type-annotated Python code
- Ready for deployment with CI/CD integration

---

## Tech Stack

- Python 3.9+
- [SQLModel](https://sqlmodel.tiangolo.com/)
- [SQLAlchemy (Async)](https://docs.sqlalchemy.org/en/20/orm/extensions/asyncio.html)
- [Alembic](https://alembic.sqlalchemy.org/en/latest/) for migrations
- Azure SQL

---

## Installation

1. Clone the repository:

```bash
git clone https://<EMAIL>/felix-sre/Felix-AI/_git/Felix-AI
cd Felix-AI
```

2. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install with poetry

```bash
pip install poetry
poetry install
```


---

## Configuration

- Configure your database connection string in the environment variable `DATABASE_URL`.
- Example for PostgreSQL:

```bash
export DATABASE_URL="postgresql+asyncpg://user:password@localhost/dbname"
```

- Ensure `alembic.ini` or your Alembic environment picks up this variable.

---

## Database Migrations

- Generate a new migration after model changes:

```bash
alembic revision --autogenerate -m "Your message"
```

- Apply migrations to the database:

```bash
alembic upgrade head
```

- Migration scripts are stored in the `alembic/versions/` directory and should be committed to version control.

---

## Running the Application

- Run your application entry point (adjust as needed):

```bash
uvicorn yourapp.main:app --reload
```

- Make sure your database is running and reachable.

---

## Testing

- Run tests with:

```bash
pytest
```

- Add tests for new features and bug fixes.

---

## CI/CD

- The CI/CD pipeline should:
    - Pull the latest code and migration scripts.
    - Run `alembic upgrade head` to apply database migrations.
    - Deploy the updated application.
- Migration scripts **must** be generated and committed locally before deployment.

---

## Contributing

Contributions are welcome! Please:

- Fork the repository
- Create a feature branch
- Submit a pull request with a clear description of changes

---

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

