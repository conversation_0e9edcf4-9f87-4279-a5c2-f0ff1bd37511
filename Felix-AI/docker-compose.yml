services:
  postgres:
    container_name: FelixAiPostgres
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: felix_ai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  felix-ai-backend:
    container_name: FelixAiBackendAppDevelop
    build:
      context: .
      dockerfile: docker/Dockerfile.simple
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@postgres:5432/felix_ai
      - MIGRATION_ENABLED=true
      - APP_HOST=0.0.0.0
      - APP_PORT=9019
      - DEBUG=true
      - SECRET_KEY=your_secret_key_here
      - ALGORITHM=HS256
      - SUPER_USERNAME=admin
      - SUPER_PASSWORD=admin_password
      - APP_CLIENT_ID=d2869a3e-4104-424e-b4c5-577fbfd3b350
      - ENTRAID_REDIRECT_URI=/docs/oauth2-redirect
      - OPENAPI_CLIENT_ID=d2869a3e-4104-424e-b4c5-577fbfd3b350
    depends_on:
      postgres:
        condition: service_healthy
    restart: always
    ports:
      - "9019:9019"

volumes:
  postgres_data: