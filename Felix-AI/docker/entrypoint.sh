#!/bin/bash

set -e

# Print Python and pip versions for debugging
echo "Python version:"
python --version
echo "Pip version:"
pip --version
echo "Installed packages:"
pip list

# Set Python path
export PYTHONPATH=/app

# Create required directories
echo "Creating required directories..."
mkdir -p /app/app/resources

if [[ "${MIGRATION_ENABLED}" == "true" ]]; then
  echo "Running migrations"
  cd /app

  # Check if required packages are installed
  echo "Checking for required packages..."

  # Check for alembic
  if ! python -c "import alembic" 2>/dev/null; then
    echo "Alembic not found, installing..."
    pip install alembic
  fi

  # Check for orjson
  if ! python -c "import orjson" 2>/dev/null; then
    echo "orjson not found, installing..."
    pip install orjson
  fi

  # Check for asyncer
  if ! python -c "import asyncer" 2>/dev/null; then
    echo "asyncer not found, installing..."
    pip install asyncer
  fi

  # Check for PyJWT
  if ! python -c "import jwt" 2>/dev/null; then
    echo "PyJWT not found, installing..."
    pip install PyJWT
  fi

  # Check for pymongo
  if ! python -c "import pymongo" 2>/dev/null; then
    echo "pymongo not found, installing..."
    pip install pymongo
  fi

  # Check for python-jose
  if ! python -c "import jose" 2>/dev/null; then
    echo "python-jose not found, installing..."
    pip install python-jose
  fi

  # Check for python-multipart
  if ! python -c "import multipart" 2>/dev/null; then
    echo "python-multipart not found, installing..."
    pip install python-multipart
  fi

  # Check for passlib
  if ! python -c "import passlib" 2>/dev/null; then
    echo "passlib not found, installing..."
    pip install passlib
  fi

  # Check for azure-storage-blob
  if ! python -c "import azure.storage.blob" 2>/dev/null; then
    echo "azure-storage-blob not found, installing..."
    pip install azure-storage-blob
  fi

  # Check for aiohttp
  if ! python -c "import aiohttp" 2>/dev/null; then
    echo "aiohttp not found, installing..."
    pip install aiohttp
  fi

  # Check for fastapi-pagination
  if ! python -c "import fastapi_pagination" 2>/dev/null; then
    echo "fastapi-pagination not found, installing..."
    pip install fastapi-pagination
  fi

  # Check for loguru
  if ! python -c "import loguru" 2>/dev/null; then
    echo "loguru not found, installing..."
    pip install loguru
  fi

  # Check for PyMuPDF (fitz)
  if ! python -c "import fitz" 2>/dev/null; then
    echo "PyMuPDF not found, installing..."
    pip install pymupdf
  fi

  # Check for Pillow (PIL)
  if ! python -c "import PIL" 2>/dev/null; then
    echo "Pillow not found, installing..."
    pip install pillow
  fi

  # Check for openpyxl
  if ! python -c "import openpyxl" 2>/dev/null; then
    echo "openpyxl not found, installing..."
    pip install openpyxl
  fi

  # Check for Azure Form Recognizer
  if ! python -c "import azure.ai.formrecognizer" 2>/dev/null; then
    echo "Azure Form Recognizer not found, installing..."
    pip install azure-ai-formrecognizer
  fi

  # Check for OpenAI
  if ! python -c "import openai" 2>/dev/null; then
    echo "OpenAI not found, installing..."
    pip install openai
  fi

  # Check for pandas
  if ! python -c "import pandas" 2>/dev/null; then
    echo "pandas not found, installing..."
    pip install pandas
  fi

  # Check for litellm
  if ! python -c "import litellm" 2>/dev/null; then
    echo "litellm not found, installing..."
    pip install litellm
  fi

  # Check for xlsxwriter
  if ! python -c "import xlsxwriter" 2>/dev/null; then
    echo "xlsxwriter not found, installing..."
    pip install xlsxwriter
  fi

  # Check for tabulate
  if ! python -c "import tabulate" 2>/dev/null; then
    echo "tabulate not found, installing..."
    pip install tabulate
  fi

  # Check database connectivity
  echo "Checking database connectivity..."
  python -m app.api.database.check_db || echo "Database connection check failed, but continuing..."

  # Initialize the database directly
  echo "Initializing database tables..."
  python -m app.api.database.init_db || echo "Database initialization failed, but continuing..."

  # Fix migration issues
  echo "Fixing migration issues..."
  python -m app.api.database.fix_migrations || echo "Migration fix failed, but continuing..."

  # Fix requests table
  echo "Fixing requests table..."
  python -m app.api.database.fix_requests_table || echo "Requests table fix failed, but continuing..."

  # Fix requests table directly with SQL
  echo "Fixing requests table directly with SQL..."
  python -m app.api.database.fix_requests_table_direct || echo "Direct SQL fix for requests failed, but continuing..."

  # Fix files table directly with SQL
  echo "Fixing files table directly with SQL..."
  python -m app.api.database.fix_files_table_direct || echo "Direct SQL fix for files failed, but continuing..."

  # Fix all tables with a comprehensive approach
  echo "Fixing all tables with a comprehensive approach..."
  python -m app.api.database.fix_all_tables || echo "Comprehensive table fix failed, but continuing..."

  # Run migrations
  echo "Running alembic migrations..."

  # Try to run migrations with 'head' target
  if ! python -m alembic upgrade head; then
    echo "Multiple heads detected, trying to upgrade to all heads..."

    # If that fails, try to upgrade to all heads
    if ! python -m alembic upgrade heads; then
      echo "Failed to run migrations with 'heads', trying to stamp the current version..."

      # If that fails too, try to stamp the current version and then upgrade
      python -m alembic stamp head || true
      python -m alembic upgrade head || true
    fi
  fi
fi

if [[ "${MODE}" == "worker" ]]; then
  celery -A worker.celery_app worker -c ${CELERY_WORKERS:-1} -l INFO
else
  echo "Starting FastAPI application..."
  cd /app && python -m uvicorn app.main:app --host 0.0.0.0 --port 9019 --workers ${APP_SERVER_WORKERS:-1}
fi
