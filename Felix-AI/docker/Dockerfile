################################
# PYTHON-BASE
# Sets up all our shared environment variables
################################
FROM python:3.11-slim as python-base

    # python
ENV PYTHONUNBUFFERED=1 \
    # prevents python creating .pyc files
    PYTHONDONTWRITEBYTECODE=1 \
    \
    # pip
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    \
    # poetry
    # https://python-poetry.org/docs/configuration/#using-environment-variables
    POETRY_VERSION=1.6.1 \
    # make poetry install to this location
    POETRY_HOME="/opt/poetry" \
    # make poetry create the virtual environment in the project's root
    # it gets named `.venv`
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    # do not ask any interactive question
    POETRY_NO_INTERACTION=1 \
    \
    # paths
    # this is where our requirements + virtual environment will live
    PYSETUP_PATH="/opt/pysetup" \
    VENV_PATH="/opt/pysetup/.venv"

RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        # deps for installing poetry
        curl \
        gnupg \
        unixodbc \
        unixodbc-dev

# prepend poetry and venv to path
ENV PATH="$POETRY_HOME/bin:$VENV_PATH/bin:$PATH"

# install unixodbc + msodbcsql18 + mssql-tools
# Note: Using the updated approach for Debian 12 (bookworm)
RUN curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > /usr/share/keyrings/microsoft-prod.gpg

RUN curl https://packages.microsoft.com/config/debian/12/prod.list > /etc/apt/sources.list.d/mssql-release.list

# Update the repository configuration to use the keyring
RUN echo "deb [arch=amd64,arm64,armhf signed-by=/usr/share/keyrings/microsoft-prod.gpg] https://packages.microsoft.com/debian/12/prod bookworm main" > /etc/apt/sources.list.d/mssql-release.list

RUN apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql18 && \
    ACCEPT_EULA=Y apt-get install -y mssql-tools18 && \
    echo 'export PATH="$PATH:/opt/mssql-tools18/bin"' >> ~/.bashrc && \
    apt-get install -y unixodbc-dev && \
    apt-get install -y libgssapi-krb5-2

ENV PATH="$PATH:/opt/mssql-tools18/bin"

################################
# BUILDER-BASE
# Used to build deps + create our virtual environment
################################
FROM python-base as builder-base
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        # deps for installing poetry
        curl \
        # deps for building python deps
        build-essential \
        python3-dev \
        libpq-dev \
        gcc \
        tesseract-ocr-eng \
        tesseract-ocr-vie \
        tesseract-ocr-tha && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# install poetry using pip instead of the installer script
RUN pip install "poetry==$POETRY_VERSION"

# Create poetry directory and add symlink
RUN mkdir -p /opt/poetry/bin
RUN ln -s /usr/local/bin/poetry /opt/poetry/bin/poetry

# copy project requirement files here to ensure they will be cached.
WORKDIR $PYSETUP_PATH
COPY requirements.txt ./

# install runtime deps from requirements.txt
RUN pip install -r requirements.txt

# Make sure alembic is installed globally
RUN pip install alembic sqlalchemy sqlmodel psycopg2-binary asyncpg


################################
# DEVELOPMENT
# Image used during development / testing
################################
FROM python-base as development
ENV FASTAPI_ENV=development
WORKDIR $PYSETUP_PATH

# copy in our built poetry + venv
COPY --from=builder-base $POETRY_HOME $POETRY_HOME
COPY --from=builder-base $PYSETUP_PATH $PYSETUP_PATH

# No need for additional dev dependencies since we're using requirements.txt

RUN apt-get update && apt-get install ffmpeg libsm6 libxext6  -y

# will become mountpoint of our code
COPY ./app /app/app
COPY ./migrations /app/migrations
COPY docker/entrypoint.sh /app/entrypoint.sh
COPY alembic.ini /app/alembic.ini
COPY requirements.txt /app/requirements.txt

# Make sure the entrypoint script is executable
RUN sed -i 's/\r$//' /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Set the working directory
RUN mkdir -p /app/app/resources
WORKDIR /app

# Create versions directory if it doesn't exist
RUN mkdir -p /app/migrations/versions

EXPOSE 9019

ENTRYPOINT ["/bin/bash","/app/entrypoint.sh"]