---
description:
globs:
alwaysApply: true
---
- You are an expert in TypeScript, Node.js, Vite, React, Shadcn UI, and Tailwind and Framer Motion.
- You are also an expert in Supabase and React integration patterns.

- Code Style and Structure
 - Write concise, technical TypeScript code with accurate examples.
 - Use functional and declarative programming patterns; avoid classes.
 - Prefer iteration and modularization over code duplication.
 - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
 - Structure files: exported component, subcomponents, helpers, static content, types.

- Project Structure
 - Components: Place reusable page components in @components/
 - Types: Define all TypeScript interfaces and types in @types directory

- Database
 - Keep all Supabase queries in @utils/supabase/queries.ts
 - Define database schema in @schema.sql
 - Never write database queries directly in components
 - API: Place all API route handlers in @app/api

- Supabase Best Practices
 - Use RLS (Row Level Security) policies for data access control
 - Implement proper error handling for all database operations
 - Use strongly typed database queries with TypeScript
 - Leverage Supabase realtime subscriptions when needed
 - Keep database schema migrations in version control
 - Use prepared statements to prevent SQL injection
- Authentication & Security
 - Use Supabase Auth for authentication
 - Implement proper session management
 - Use middleware for protected routes
 - Never expose sensitive data on the client
 - Validate all user input
- Naming Conventions
 - Favor named exports for components.
- TypeScript Usage
 - Use TypeScript for all code; prefer interfaces over types.
 - Avoid enums; use maps instead.
 - Use functional components with TypeScript interfaces.
- Syntax and Formatting
 - Use the "function" keyword for pure functions.
 - Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
 - Use declarative JSX.
- UI and Styling
 - Use Shadcn UI, and Tailwind for components and styling.
 - Implement responsive design with Tailwind CSS; use a mobile-first approach.
- Performance Optimization
 - Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
 - Wrap client components in Suspense with fallback.
 - Use dynamic loading for non-critical components.
 - Optimize images: use WebP format, include size data, implement lazy loading.
- Code Generation
 - Use consistent patterns for generated code
 - Follow TypeScript best practices in generated code
 - Ensure generated components follow project structure guidelines
 - Maintain proper error handling in generated code
- Key Conventions
 - Use 'nuqs' for URL search parameter state management.
 - Optimize Web Vitals (LCP, CLS, FID).
 - While creating placeholder images as a part of your seed data, use https://picsum.photos/
 - If a user messages in a foreign language, respond in that language
- Modular Approach: As your project grows, consider adopting a more modular structure, where each feature or domain has its own folder containing components, hooks, and utilities specific to that feature
- Do not delete debug console log except I told you to do