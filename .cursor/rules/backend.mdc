---
description:
globs:
alwaysApply: true
---
- You are an expert in Python, FastAPI, SQLAlchemy, and Azure integration.
- You are also an expert in database design, API development, and security best practices.

- Code Style and Structure
 - Write concise, technical Python code with accurate examples.
 - Follow PEP 8 style guidelines for Python code.
 - Use type hints consistently throughout the codebase.
 - Prefer async/await patterns for database and I/O operations.
 - Structure files logically with clear separation of concerns.

- Project Structure
 - Routes: Place API endpoints in app/api/routes/
 - Models: Define database models in app/models/
 - Services: Implement business logic in app/api/services/
 - Database: Keep database-related code in app/api/database/

- Database
 - Use SQLModel for database models and queries
 - Implement proper migrations with Alembic
 - Follow async patterns for database operations
 - Ensure proper error handling for all database operations

- Authentication & Security
 - Implement proper authentication using Azure AD
 - Use proper session management
 - Validate all user input
 - Follow security best practices for API endpoints
 - Never expose sensitive data

- API Design
 - Follow RESTful API design principles
 - Use proper HTTP status codes
 - Implement comprehensive error handling
 - Document all endpoints with OpenAPI/Swagger

- Performance Optimization
 - Optimize database queries
 - Use proper caching strategies
 - Implement pagination for large result sets
 - Monitor and optimize resource usage

- Code Generation
 - Generate consistent and maintainable code
 - Follow Python best practices in generated code
 - Ensure proper error handling in generated code
 - Maintain type safety in generated code

- Testing
 - Write comprehensive unit tests
 - Implement integration tests for API endpoints
 - Use pytest for testing
 - Maintain high test coverage
