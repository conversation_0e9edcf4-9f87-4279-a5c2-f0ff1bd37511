apiVersion: apps/v1
kind: Deployment
metadata:
  name: felix-frontend
  namespace: felix-ai
  labels:
    app: felix-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: felix-frontend
  template:
    metadata:
      labels:
        app: felix-frontend
    spec:
      containers:
      - name: felix-frontend
        image: crfelixdevswedencentralih0.azurecr.io/felix-ai-fe:latest
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: felix-frontend-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: felix-frontend-service
  namespace: felix-ai
spec:
  selector:
    app: felix-frontend
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: felix-frontend-ingress
  namespace: felix-ai
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  rules:
  - host: felix-ai.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: felix-frontend-service
            port:
              number: 80
