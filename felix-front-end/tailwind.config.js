/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1A3A4A',
          50: '#E7EEF1',
          100: '#CFDDE3',
          200: '#A0BBC7',
          300: '#7199AB',
          400: '#41778F',
          500: '#1A3A4A',
          600: '#17343F',
          700: '#122B34',
          800: '#0E2229',
          900: '#09191E',
          950: '#061114'
        },
        accent: {
          DEFAULT: '#70E08D',
          50: '#F1FCF4',
          100: '#E3F9E9',
          200: '#C8F2D3',
          300: '#ACECBD',
          400: '#91E6A8',
          500: '#70E08D',
          600: '#42D769',
          700: '#25BE4F',
          800: '#1D923D',
          900: '#16672B',
          950: '#114F21'
        },
        gray: {
          50: '#F5F7FA',
          100: '#EAF0F6',
          200: '#D6E1ED',
          300: '#BBCDE0',
          400: '#A0AEC0',
          500: '#8696A7',
          600: '#667A8E',
          700: '#4A5568',
          800: '#323A46',
          900: '#1A1F27',
          950: '#0D1014'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Inter', 'system-ui', 'sans-serif']
      },
      animation: {
        'bounce-slow': 'bounce 3s infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'progress': 'progress 2s ease-in-out infinite',
        overlayShow: 'overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1)',
        contentShow: 'contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1)',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 }
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 }
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 }
        },
        progress: {
          '0%': { width: '0%' },
          '50%': { width: '70%' },
          '100%': { width: '100%' }
        },
        overlayShow: {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        contentShow: {
          from: { opacity: '0', transform: 'translate(-50%, -48%) scale(0.96)' },
          to: { opacity: '1', transform: 'translate(-50%, -50%) scale(1)' },
        },
      }
    }
  },
  plugins: []
};