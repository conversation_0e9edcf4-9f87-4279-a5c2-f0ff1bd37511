# Runtime Environment Variables for Felix-AI Frontend

This document explains how to use runtime environment variables in the Felix-AI frontend application.

## Overview

The Felix-AI frontend now supports dynamic environment variables that can be injected at runtime. This means:

1. Environment variables can be changed without rebuilding the application
2. The application can read environment variables from Kubernetes ConfigMaps
3. Different environments (dev, test, prod) can use the same Docker image with different configurations

## How It Works

The solution consists of several components:

1. A script (`generate-env-config.sh`) that generates a JavaScript file with environment variables at container startup
2. Integration with the nginx Docker image's entrypoint system to run our scripts at container startup
3. The HTML file loads this generated JavaScript file before the application code
4. A utility (`runtimeEnv.ts`) to access these environment variables in the application code

The solution leverages the nginx Docker image's entrypoint system, which automatically executes scripts in the `/docker-entrypoint.d/` directory at container startup. This ensures our environment configuration is generated before nginx starts serving the application.

## Available Environment Variables

The following environment variables can be configured at runtime:

| Variable | Description | Default |
|----------|-------------|---------|
| VITE_BACKEND_URL | URL of the backend API | http://localhost:8000 |
| VITE_TENANT_ID | Microsoft Entra ID tenant ID | - |
| VITE_CLIENT_ID | Microsoft Entra ID client ID | - |
| VITE_REDIRECT_URI | OAuth redirect URI | /oauth2-redirect |
| VITE_TOKEN_SCOPE | Authentication token scope | - |

## Build the Docker Image
docker build -t felix-frontend -f docker/Dockerfile .

## Usage in Docker

When running the application with Docker, you can set environment variables in the `docker-compose.yml` file:

```yaml
services:
  felix-ai-frontend:
    container_name: FelixAiFrontendApp
    build:
      context: .
      dockerfile: docker/Dockerfile
    restart: always
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - VITE_BACKEND_URL=https://afd-ep-felix-dev-swedencentral-esh6frdrbwbsaya7.z03.azurefd.net
      - VITE_TENANT_ID=6b62a0db-05fd-4be0-84d3-9636f20c8cab
      - VITE_CLIENT_ID=d2869a3e-4104-424e-b4c5-577fbfd3b350
      - VITE_REDIRECT_URI=/oauth2-redirect
      - VITE_TOKEN_SCOPE=api://cd8eaec4-169a-4d39-9c41-4fe34935a310/user_impersonation
```

Or you can pass them directly to the `docker run` command:

```bash
docker run -p 3000:80 \
  -e VITE_BACKEND_URL=https://afd-ep-felix-dev-swedencentral-esh6frdrbwbsaya7.z03.azurefd.net \
  -e VITE_TENANT_ID=6b62a0db-05fd-4be0-84d3-9636f20c8cab \
  -e VITE_CLIENT_ID=d2869a3e-4104-424e-b4c5-577fbfd3b350 \
  -e VITE_REDIRECT_URI=/oauth2-redirect \
  -e VITE_TOKEN_SCOPE=api://cd8eaec4-169a-4d39-9c41-4fe34935a310/user_impersonation \
  felix-frontend
```

## Usage in Kubernetes

When deploying to Kubernetes, you can use ConfigMaps to manage environment variables:

1. Create a ConfigMap with your environment variables:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: felix-frontend-config
  namespace: felix-ai
data:
  VITE_BACKEND_URL: "https://afd-ep-felix-dev-swedencentral-esh6frdrbwbsaya7.z03.azurefd.net"
  VITE_TENANT_ID: "6b62a0db-05fd-4be0-84d3-9636f20c8cab"
  VITE_CLIENT_ID: "d2869a3e-4104-424e-b4c5-577fbfd3b350"
  VITE_REDIRECT_URI: "/oauth2-redirect"
  VITE_TOKEN_SCOPE: "api://cd8eaec4-169a-4d39-9c41-4fe34935a310/user_impersonation"
```

2. Reference the ConfigMap in your Deployment:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: felix-frontend
spec:
  template:
    spec:
      containers:
      - name: felix-frontend
        image: crfelixdevswedencentralih0.azurecr.io/felix-ai-fe:latest
        envFrom:
        - configMapRef:
            name: felix-frontend-config
```

## Usage in Development

For local development, you can still use the `.env` file as before. The application will first try to use runtime environment variables, and if they're not available, it will fall back to the build-time environment variables from Vite.

## Accessing Environment Variables in Code

To access environment variables in your code, use the `getEnv` function from the `runtimeEnv` utility:

```typescript
import { getEnv } from './utils/runtimeEnv';

// Get a specific environment variable
const backendUrl = getEnv('BACKEND_URL');
const tenantId = getEnv('TENANT_ID');

// Get all environment variables
import { getAllEnv } from './utils/runtimeEnv';
const env = getAllEnv();
console.log(env.BACKEND_URL);
```

## Troubleshooting

If you're having issues with environment variables:

1. Check that the `env-config.js` file is being generated correctly in the container
2. Verify that the file is being loaded in the browser (check the network tab)
3. Check the browser console for any errors
4. Verify that the environment variables are being passed correctly to the container

You can inspect the generated `env-config.js` file by running:

```bash
docker exec -it felix-ai-frontend cat /usr/share/nginx/html/env-config.js
```

You can also check the container logs to see if there were any errors during startup:

```bash
docker logs felix-ai-frontend
```

If you need to debug the container, you can run a shell inside it:

```bash
docker exec -it felix-ai-frontend sh
```

Common issues:

1. **Missing env-config.js file**: Check if the file was generated by running `ls -la /usr/share/nginx/html/` inside the container.
2. **Permission issues**: Make sure the scripts in `/docker-entrypoint.d/` are executable.
3. **Environment variables not being passed**: Verify that you're passing the environment variables correctly to the container.
4. **Browser caching**: Try clearing your browser cache or using incognito mode to ensure you're getting the latest version of the application.
