@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply font-sans text-gray-700 antialiased bg-white;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display text-primary font-semibold;
  }

  h1 {
    @apply text-4xl md:text-5xl leading-tight;
  }

  h2 {
    @apply text-3xl md:text-4xl leading-tight;
  }

  h3 {
    @apply text-2xl md:text-3xl leading-tight;
  }

  h4 {
    @apply text-xl md:text-2xl leading-tight;
  }

  h5 {
    @apply text-lg md:text-xl leading-tight;
  }

  h6 {
    @apply text-base md:text-lg leading-tight;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium text-base transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-accent text-white hover:bg-accent-600 focus:ring-accent;
  }

  .btn-secondary {
    @apply btn bg-white text-primary border border-primary hover:bg-gray-50 focus:ring-primary;
  }

  .btn-outline {
    @apply btn bg-transparent text-primary border border-primary hover:bg-primary hover:text-white focus:ring-primary;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-100 transition-all hover:shadow-md;
  }

  .input {
    @apply block w-full px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent transition-colors;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}