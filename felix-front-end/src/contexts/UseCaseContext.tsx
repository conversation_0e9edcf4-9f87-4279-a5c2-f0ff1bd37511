import { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { UseCase } from '../data/useCases';
import {
  saveUseCaseRequestId,
  getUseCaseRequestId,
  saveUseCaseResults,
  getUseCaseResults
} from '../utils/localStorage';

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  status: 'uploading' | 'validating' | 'valid' | 'error';
  path?: string;
  errorMessage?: string;
  file?: File;
}

interface UseCaseContextData {
  // Request data
  requestId: string;
  setRequestId: (id: string) => void;

  // Files data
  files: UploadedFile[];
  addFiles: (newFiles: UploadedFile[]) => void;
  updateFileStatus: (fileId: string, status: UploadedFile['status'], errorMessage?: string) => void;
  removeFile: (fileId: string) => void;

  // Processing status
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  processingError: string | null;
  setProcessingError: (error: string | null) => void;
  liveRequestStatus: string | null;
  setLiveRequestStatus: (status: string | null) => void;

  // Config data for different use cases
  config: {
    materialityThreshold: string;
    materialityLogic: 'OR' | 'AND';
    fixedAmount: string;
    topX: string;
    referenceSource: string;
    comparisonPeriod: string;
    roundingRule: string;
    correctionPriority: string;
  };
  updateConfig: (key: string, value: string) => void;

  // Processing results
  processingResults: any;
  setProcessingResults: (results: any) => void;

  // Current use case
  currentUseCase?: UseCase;
  setCurrentUseCase: (useCase?: UseCase) => void;
}

const UseCaseContext = createContext<UseCaseContextData | undefined>(undefined);

export function UseCaseProvider({ children }: { children: ReactNode }) {
  const [requestId, setRequestIdState] = useState<string>('');
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processingError, setProcessingError] = useState<string | null>(null);
  const [liveRequestStatus, setLiveRequestStatus] = useState<string | null>(null);
  const [config, setConfig] = useState({
    materialityThreshold: '5',
    materialityLogic: 'OR' as const,
    fixedAmount: '10000',
    topX: '10',
    referenceSource: 'treasury',
    comparisonPeriod: 'day',
    roundingRule: 'nearest',
    correctionPriority: 'totals'
  });
  const [processingResults, setProcessingResultsState] = useState<any>(null);
  const [currentUseCase, setCurrentUseCase] = useState<UseCase | undefined>(undefined);

  // Set request ID and save to storage
  const setRequestId = (id: string) => {
    setRequestIdState(id);
    if (currentUseCase) {
      saveUseCaseRequestId(currentUseCase.id, id);
    }
  };

  // Set processing results and save to storage
  const setProcessingResults = (results: any) => {
    setProcessingResultsState(results);
    if (currentUseCase) {
      saveUseCaseResults(currentUseCase.id, results);
    }
  };

  // Load saved data when use case changes
  useEffect(() => {
    if (currentUseCase) {
      const useCaseId = currentUseCase.id;

      // Load request ID
      const savedRequestId = getUseCaseRequestId(useCaseId);
      if (savedRequestId) {
        setRequestIdState(savedRequestId);
      }

      // Load processing results
      const savedResults = getUseCaseResults(useCaseId);
      if (savedResults) {
        setProcessingResultsState(savedResults);
      }
    }
  }, [currentUseCase]);

  const addFiles = (newFiles: UploadedFile[]) => {
    setFiles(prev => [...prev, ...newFiles]);
  };

  const updateFileStatus = (fileId: string, status: UploadedFile['status'], errorMessage?: string) => {
    setFiles(prev =>
      prev.map(file =>
        file.id === fileId
          ? { ...file, status, errorMessage }
          : file
      )
    );
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const updateConfig = (key: string, value: string) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  return (
    <UseCaseContext.Provider
      value={{
        requestId,
        setRequestId,
        files,
        addFiles,
        updateFileStatus,
        removeFile,
        isProcessing,
        setIsProcessing,
        processingError,
        setProcessingError,
        liveRequestStatus,
        setLiveRequestStatus,
        config,
        updateConfig,
        processingResults,
        setProcessingResults,
        currentUseCase,
        setCurrentUseCase
      }}
    >
      {children}
    </UseCaseContext.Provider>
  );
}

export function useUseCaseContext() {
  const context = useContext(UseCaseContext);
  if (context === undefined) {
    throw new Error('useUseCaseContext must be used within a UseCaseProvider');
  }
  return context;
}