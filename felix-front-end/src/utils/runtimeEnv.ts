/**
 * Runtime environment utility
 * 
 * This utility provides access to environment variables that are injected at runtime
 * through the env-config.js file. It falls back to Vite's import.meta.env for local development.
 */

// Define the shape of our runtime environment
interface RuntimeEnvironment {
  BACKEND_URL: string;
  TENANT_ID: string;
  CLIENT_ID: string;
  REDIRECT_URI: string;
  TOKEN_SCOPE: string;
  [key: string]: string;
}

// Declare the _env_ property on the window object
declare global {
  interface Window {
    _env_?: RuntimeEnvironment;
  }
}

/**
 * Get an environment variable from runtime config or build-time config
 * 
 * @param key The environment variable key (without the VITE_ prefix)
 * @param defaultValue Optional default value if the environment variable is not found
 * @returns The environment variable value or the default value
 */
export function getEnv(key: keyof RuntimeEnvironment, defaultValue: string = ''): string {
  // First try to get from runtime environment (window._env_)
  if (window._env_ && window._env_[key] !== undefined && window._env_[key] !== '') {
    return window._env_[key];
  }
  
  // Then try to get from Vite's import.meta.env (for local development)
  const viteKey = `VITE_${key}` as keyof ImportMetaEnv;
  if (import.meta.env[viteKey] !== undefined && import.meta.env[viteKey] !== '') {
    return import.meta.env[viteKey] as string;
  }
  
  // Fall back to default value
  return defaultValue;
}

/**
 * Get all environment variables
 * 
 * @returns An object containing all environment variables
 */
export function getAllEnv(): RuntimeEnvironment {
  return {
    BACKEND_URL: getEnv('BACKEND_URL', 'http://localhost:8000'),
    TENANT_ID: getEnv('TENANT_ID', ''),
    CLIENT_ID: getEnv('CLIENT_ID', ''),
    REDIRECT_URI: getEnv('REDIRECT_URI', '/oauth2-redirect'),
    TOKEN_SCOPE: getEnv('TOKEN_SCOPE', ''),
  };
}

export default {
  getEnv,
  getAllEnv,
};
