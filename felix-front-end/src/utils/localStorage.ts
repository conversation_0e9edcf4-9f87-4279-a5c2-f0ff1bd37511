/**
 * Utility functions for managing persistent storage for use cases
 * Used to persist state across page reloads and browser sessions
 */

// Keys for storage
const KEYS = {
  CURRENT_USE_CASE: 'felix_current_use_case',
  USE_CASE_STEPS: 'felix_use_case_steps',
  USE_CASE_DATA: 'felix_use_case_data',
};

// Interface for use case step data
interface UseCaseStepData {
  currentStep: number;
  stepPath: string;
  status?: string;
  lastUpdated: number;
}

// Interface for storing all use case steps
interface UseCaseSteps {
  [useCaseId: string]: UseCaseStepData;
}

// Interface for storing use case data (files, config, etc.)
interface UseCaseData {
  [useCaseId: string]: {
    requestId?: string;
    processingResults?: any;
    lastUpdated: number;
  };
}

/**
 * Save the current use case ID to storage
 */
export const saveUseCaseId = (useCaseId: string): void => {
  localStorage.setItem(KEYS.CURRENT_USE_CASE, useCaseId);
  console.log(`[Storage] Saved current use case ID: ${useCaseId}`);
};

/**
 * Get the saved use case ID from storage
 */
export const getSavedUseCaseId = (): string | null => {
  const useCaseId = localStorage.getItem(KEYS.CURRENT_USE_CASE);
  console.log(`[Storage] Retrieved use case ID: ${useCaseId}`);
  return useCaseId;
};

/**
 * Save the current step information for a specific use case
 */
export const saveUseCaseStep = (
  useCaseId: string,
  step: number,
  path: string,
  status?: string
): void => {
  try {
    // Get existing steps data or initialize new object
    const stepsData: UseCaseSteps = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_STEPS) || '{}'
    );

    // Update the step data for this use case
    stepsData[useCaseId] = {
      currentStep: step,
      stepPath: path,
      status: status,
      lastUpdated: Date.now()
    };

    // Save back to storage
    localStorage.setItem(KEYS.USE_CASE_STEPS, JSON.stringify(stepsData));
    console.log(`[Storage] Saved step data for use case ${useCaseId}:`, stepsData[useCaseId]);
  } catch (error) {
    console.error('[Storage] Error saving use case step:', error);
  }
};

/**
 * Get the saved step information for a specific use case
 */
export const getSavedUseCaseStep = (useCaseId: string): UseCaseStepData | null => {
  try {
    const stepsData: UseCaseSteps = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_STEPS) || '{}'
    );

    const stepData = stepsData[useCaseId];
    console.log(`[Storage] Retrieved step data for use case ${useCaseId}:`, stepData);
    return stepData || null;
  } catch (error) {
    console.error('[Storage] Error retrieving use case step:', error);
    return null;
  }
};

/**
 * Get the saved current step number for a specific use case
 */
export const getSavedCurrentStep = (useCaseId: string): number | null => {
  const stepData = getSavedUseCaseStep(useCaseId);
  return stepData ? stepData.currentStep : null;
};

/**
 * Get the saved step path for a specific use case
 */
export const getSavedStepPath = (useCaseId: string): string | null => {
  const stepData = getSavedUseCaseStep(useCaseId);
  return stepData ? stepData.stepPath : null;
};

/**
 * Get the saved step status for a specific use case
 */
export const getSavedStepStatus = (useCaseId: string): string | null => {
  const stepData = getSavedUseCaseStep(useCaseId);
  return stepData?.status || null;
};

/**
 * Update the status of a specific use case step
 */
export const updateUseCaseStepStatus = (useCaseId: string, status: string): void => {
  try {
    const stepsData: UseCaseSteps = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_STEPS) || '{}'
    );

    if (stepsData[useCaseId]) {
      stepsData[useCaseId].status = status;
      stepsData[useCaseId].lastUpdated = Date.now();
      localStorage.setItem(KEYS.USE_CASE_STEPS, JSON.stringify(stepsData));
      console.log(`[Storage] Updated status for use case ${useCaseId} to: ${status}`);
    }
  } catch (error) {
    console.error('[Storage] Error updating use case step status:', error);
  }
};

/**
 * Save request ID for a specific use case
 */
export const saveUseCaseRequestId = (useCaseId: string, requestId: string): void => {
  try {
    // Get existing data or initialize new object
    const useCaseData: UseCaseData = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_DATA) || '{}'
    );

    // Initialize if not exists
    if (!useCaseData[useCaseId]) {
      useCaseData[useCaseId] = {
        lastUpdated: Date.now()
      };
    }

    // Update the request ID
    useCaseData[useCaseId].requestId = requestId;
    useCaseData[useCaseId].lastUpdated = Date.now();

    // Save back to storage
    localStorage.setItem(KEYS.USE_CASE_DATA, JSON.stringify(useCaseData));
    console.log(`[Storage] Saved request ID for use case ${useCaseId}: ${requestId}`);
  } catch (error) {
    console.error('[Storage] Error saving use case request ID:', error);
  }
};

/**
 * Get request ID for a specific use case
 */
export const getUseCaseRequestId = (useCaseId: string): string | null => {
  try {
    const useCaseData: UseCaseData = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_DATA) || '{}'
    );

    const requestId = useCaseData[useCaseId]?.requestId || null;
    console.log(`[Storage] Retrieved request ID for use case ${useCaseId}: ${requestId}`);
    return requestId;
  } catch (error) {
    console.error('[Storage] Error retrieving use case request ID:', error);
    return null;
  }
};

/**
 * Save processing results for a specific use case
 */
export const saveUseCaseResults = (useCaseId: string, results: any): void => {
  try {
    // Get existing data or initialize new object
    const useCaseData: UseCaseData = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_DATA) || '{}'
    );

    // Initialize if not exists
    if (!useCaseData[useCaseId]) {
      useCaseData[useCaseId] = {
        lastUpdated: Date.now()
      };
    }

    // Update the results
    useCaseData[useCaseId].processingResults = results;
    useCaseData[useCaseId].lastUpdated = Date.now();

    // Save back to storage
    localStorage.setItem(KEYS.USE_CASE_DATA, JSON.stringify(useCaseData));
    console.log(`[Storage] Saved results for use case ${useCaseId}`);
  } catch (error) {
    console.error('[Storage] Error saving use case results:', error);
  }
};

/**
 * Get processing results for a specific use case
 */
export const getUseCaseResults = (useCaseId: string): any | null => {
  try {
    const useCaseData: UseCaseData = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_DATA) || '{}'
    );

    const results = useCaseData[useCaseId]?.processingResults || null;
    console.log(`[Storage] Retrieved results for use case ${useCaseId}`);
    return results;
  } catch (error) {
    console.error('[Storage] Error retrieving use case results:', error);
    return null;
  }
};

/**
 * Clear all data for a specific use case
 */
export const clearUseCaseData = (useCaseId: string): void => {
  try {
    // Clear step data
    const stepsData: UseCaseSteps = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_STEPS) || '{}'
    );

    if (stepsData[useCaseId]) {
      delete stepsData[useCaseId];
      localStorage.setItem(KEYS.USE_CASE_STEPS, JSON.stringify(stepsData));
    }

    // Clear use case data
    const useCaseData: UseCaseData = JSON.parse(
      localStorage.getItem(KEYS.USE_CASE_DATA) || '{}'
    );

    if (useCaseData[useCaseId]) {
      delete useCaseData[useCaseId];
      localStorage.setItem(KEYS.USE_CASE_DATA, JSON.stringify(useCaseData));
    }

    console.log(`[Storage] Cleared all data for use case ${useCaseId}`);

    // If this was the current use case, clear that too
    if (getSavedUseCaseId() === useCaseId) {
      localStorage.removeItem(KEYS.CURRENT_USE_CASE);
    }
  } catch (error) {
    console.error('[Storage] Error clearing use case data:', error);
  }
};

/**
 * Clear all use case related storage
 */
export const clearAllUseCaseData = (): void => {
  localStorage.removeItem(KEYS.CURRENT_USE_CASE);
  localStorage.removeItem(KEYS.USE_CASE_STEPS);
  localStorage.removeItem(KEYS.USE_CASE_DATA);
  console.log('[Storage] Cleared all use case data');
};

/**
 * Check if there is saved data for a specific use case
 */
export const hasSavedUseCaseData = (useCaseId: string): boolean => {
  return getSavedUseCaseStep(useCaseId) !== null;
};
