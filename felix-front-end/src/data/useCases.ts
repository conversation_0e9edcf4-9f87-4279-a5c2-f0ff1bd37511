import { BarChart3, CircleDollarSign, Network, Calculator } from 'lucide-react';

export interface UseCase {
  id: string;
  name: string;
  description: string;
  icon: keyof typeof icons;
  value: string;
  input: string;
  requiresCommunication: boolean;
  endProcess: {
    summary: string[];
    verifications: string[];
    report: {
      timestamp: boolean;
      status: boolean;
      metrics: string[];
    };
  };
}

// Icons mapping for type safety
export const icons = {
  BarChart3,
  CircleDollarSign,
  Network,
  Calculator
};

export const useCases: UseCase[] = [
  {
    id: "analytical-review",
    name: "Analytical Review",
    description: "This initial quality control analyzes financial statement variances actuals, budgets, prior-year. It detects errors pre-consolidation, identifies business trends, and improves the accuracy of consolidated financial statements.",
    icon: "BarChart3",
    value: "Detect potential errors before they impact your consolidated statements",
    input: "Financial statements with actuals, budget, and prior year data",
    requiresCommunication: true,
    endProcess: {
      summary: [
        "Total financial statements processed",
        "Variance analysis duration",
        "Extraction success rate",
        "Number of variance notifications sent",
        "List of completed analytical reviews",
        "Any validation errors encountered"
      ],
      verifications: [
        "Automatic deletion of uploaded financial statements",
        "Removal of all variance analysis data",
        "Cleanup of temporary calculation files"
      ],
      report: {
        timestamp: true,
        status: true,
        metrics: [
          "Average processing time per statement",
          "Variance detection accuracy",
          "Communication response rate"
        ]
      }
    }
  },
  {
    id: "exchange-rate-review",
    name: "Exchange Rate Review",
    description: "Validates currency translation exchange rates against Group reference rates. This ensures consistent, accurate conversions and prevents consolidation errors.",
    icon: "CircleDollarSign",
    value: "Ensure consistent currency conversion across your global operations",
    input: "Treasury exchange rates and consolidation system rates",
    requiresCommunication: false,
    endProcess: {
      summary: [
        "Total currency pairs processed",
        "Exchange rate validation duration",
        "Rate comparison success rate",
        "Number of rate adjustments made",
        "List of completed rate validations",
        "Rate discrepancies identified and resolved"
      ],
      verifications: [
        "Automatic deletion of uploaded exchange rate files",
        "Removal of all rate validation data",
        "Cleanup of temporary rate comparison files"
      ],
      report: {
        timestamp: true,
        status: true,
        metrics: [
          "Average validation time per currency pair",
          "Rate comparison accuracy rate",
          "Financial impact of rate corrections"
        ]
      }
    }
  },
  {
    id: "intercompany-reconciliation",
    name: "Intercompany Reconciliation",
    description: "Resolve intercompany discrepancies pre-consolidation by matching multi-currency balances. This ensures accurate eliminations and allows defining materiality thresholds for variances based on account sensitivity.",
    icon: "Network",
    value: "Streamline elimination entries with automated variance detection",
    input: "Intercompany reconciliation tables with assets/liabilities balances",
    requiresCommunication: true,
    endProcess: {
      summary: [
        "Total intercompany balances processed",
        "Reconciliation process duration",
        "Balance matching success rate",
        "Number of reconciliation notifications sent",
        "List of completed reconciliations",
        "Outstanding reconciliation items"
      ],
      verifications: [
        "Automatic deletion of uploaded balance files",
        "Removal of all matching analysis data",
        "Cleanup of temporary reconciliation files"
      ],
      report: {
        timestamp: true,
        status: true,
        metrics: [
          "Average processing time per balance pair",
          "Reconciliation matching accuracy",
          "Entity response completion rate"
        ]
      }
    }
  },
  {
    id: "rounding-issue-resolution",
    name: "Rounding Issue Resolution",
    description: "This final pre-publication check ensures mathematical accuracy, consistency, and IFRS compliance by correcting inconsistencies, maintaining accuracy, and documenting rounding adjustments",
    icon: "Calculator",
    value: "Publish with confidence knowing your statements are mathematically sound",
    input: "Financial statements in IFRS format requiring final quality checks",
    requiresCommunication: false,
    endProcess: {
      summary: [
        "Total financial statements processed",
        "Rounding analysis duration",
        "Correction implementation rate",
        "Number of rounding adjustments applied",
        "List of completed rounding corrections",
        "Cross-reference integrity verified"
      ],
      verifications: [
        "Automatic deletion of uploaded financial statements",
        "Removal of all rounding analysis data",
        "Cleanup of temporary adjustment files"
      ],
      report: {
        timestamp: true,
        status: true,
        metrics: [
          "Average processing time per financial statement",
          "Rounding correction accuracy rate",
          "Mathematical consistency score"
        ]
      }
    }
  }
];

export function getUseCaseById(id: string): UseCase | undefined {
  return useCases.find(useCase => useCase.id === id);
}