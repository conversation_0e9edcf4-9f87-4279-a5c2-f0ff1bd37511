import { useState, useEffect } from 'react';
import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { BarChart3, Clock, FileText, Layers, LogOut, Menu, Settings as SettingsIcon, X, PanelLeftClose, PanelLeft, CircleDollarSign, Network, Calculator } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import Logo from '../components/common/Logo';
import { useCases } from '../data/useCases';
import { getSavedUseCaseStep, hasSavedUseCaseData } from '../utils/sessionStorage';

// Custom NavLink component that handles use case navigation with saved steps
function UseCaseNavLink({ to, useCaseId, children, className, onClick, title }: {
  to: string;
  useCaseId: string;
  children: React.ReactNode;
  className: ({ isActive }: { isActive: boolean }) => string;
  onClick?: () => void;
  title?: string;
}) {
  const navigate = useNavigate();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Check if there's a saved step for this use case
    if (hasSavedUseCaseData(useCaseId)) {
      const stepData = getSavedUseCaseStep(useCaseId);
      if (stepData && stepData.stepPath) {
        console.log(`[DashboardLayout] Navigating to saved step for ${useCaseId}: ${stepData.stepPath}`);
        navigate(`/dashboard/use-case/${useCaseId}${stepData.stepPath}`);
        if (onClick) onClick();
        return;
      }
    }

    // No saved step, navigate to the default path
    console.log(`[DashboardLayout] No saved step for ${useCaseId}, navigating to default path`);
    navigate(to);
    if (onClick) onClick();
  };

  return (
    <NavLink
      to={to}
      className={className}
      onClick={handleClick}
      title={title}
    >
      {children}
    </NavLink>
  );
}

export default function DashboardLayout() {
  const { user, logout } = useAuth();
  const [formData, setFormData] = useState<{ companyName: string } | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isNavCollapsed, setIsNavCollapsed] = useState(false);

  // Fetch company name on mount
  useEffect(() => {
    // Simulated API call to get company settings
    const fetchCompanyData = async () => {
      try {
        // This would be an actual API call in production
        await new Promise(resolve => setTimeout(resolve, 100));
        setFormData({ companyName: 'Global Enterprises AG' });
      } catch (error) {
        console.error('Failed to fetch company data:', error);
      }
    };

    fetchCompanyData();
  }, []);
  const navItems = [
    {
      icon: <Layers size={20} />,
      label: 'Overview',
      href: '/dashboard'
    },
    {
      type: 'group',
      label: 'Use Cases',
      items: useCases.map(useCase => ({
        icon: useCase.icon === 'BarChart3' ? <BarChart3 size={20} /> :
              useCase.icon === 'CircleDollarSign' ? <CircleDollarSign size={20} /> :
              useCase.icon === 'Network' ? <Network size={20} /> :
              <Calculator size={20} />,
        label: useCase.name,
        href: `/dashboard/use-case/${useCase.id}`
      }))
    },
    {
      icon: <FileText size={20} />,
      label: 'Active Processes',
      href: '/dashboard/active-processes'
    },
    {
      icon: <Clock size={20} />,
      label: 'History',
      href: '/dashboard/history'
    },
    {
      icon: <SettingsIcon size={20} />,
      label: 'Settings',
      href: '/dashboard/settings'
    }
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar - Desktop */}
      <aside className={`hidden md:flex flex-col bg-primary text-white transition-all duration-300 ${
        isNavCollapsed ? 'w-20' : 'w-64'
      }`}>
        <div className="p-6 flex items-center justify-between">
          {!isNavCollapsed && <Logo variant="white" />}
          <button
            onClick={() => setIsNavCollapsed(!isNavCollapsed)}
            className="text-white hover:text-accent transition-colors"
          >
            {isNavCollapsed ? <PanelLeft size={20} /> : <PanelLeftClose size={20} />}
          </button>
        </div>

        <nav className="flex-1 px-4 py-6">
          <ul className="space-y-1">
            {navItems.map((item, index) => (
              'type' in item ? (
                <li key={index} className="space-y-1">
                  {!isNavCollapsed && (
                    <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                      {item.label}
                    </div>
                  )}
                  <ul className="space-y-1">
                    {item.items?.map((subItem) => {
                      // Extract the use case ID from the href
                      const useCaseId = subItem.href.split('/').pop();

                      return (
                        <li key={subItem.label}>
                          <UseCaseNavLink
                            to={subItem.href}
                            useCaseId={useCaseId || ''}
                            className={({ isActive }) =>
                              `flex items-center px-4 py-2 text-sm rounded-md transition-colors ${
                                isActive
                                  ? 'bg-white/10 text-white'
                                  : 'text-gray-300 hover:bg-white/5 hover:text-white'
                              }`
                            }
                            title={isNavCollapsed ? subItem.label : undefined}
                          >
                            <span className="mr-3">{subItem.icon}</span>
                            {!isNavCollapsed && <span>{subItem.label}</span>}
                          </UseCaseNavLink>
                        </li>
                      );
                    })}
                  </ul>
                </li>
              ) : (
                <li key={item.label}>
                  <NavLink
                    to={item.href}
                    end
                    className={({ isActive }) =>
                      `flex items-center px-4 py-3 rounded-md transition-colors ${
                        isActive
                          ? 'bg-white/10 text-white'
                          : 'text-gray-300 hover:bg-white/5 hover:text-white'
                      }`
                    }
                    title={isNavCollapsed ? item.label : undefined}
                  >
                    <span className="mr-3">{item.icon}</span>
                    {!isNavCollapsed && <span>{item.label}</span>}
                  </NavLink>
                </li>
              )
            ))}
          </ul>
        </nav>

        <div className="p-4 border-t border-white/10">
          <button
            onClick={logout}
            className="flex items-center px-4 py-2 w-full text-gray-300 hover:text-white rounded-md transition-colors hover:bg-white/5"
            title={isNavCollapsed ? "Log Out" : undefined}
          >
            <LogOut size={20} className="mr-3" />
            {!isNavCollapsed && <span>Log Out</span>}
          </button>
        </div>
      </aside>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header */}
        <header className="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
          {/* Mobile menu toggle */}
          <button
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(true)}
          >
            <Menu size={24} />
          </button>

          <div className="md:hidden">
            <Logo />
          </div>
          <div className="hidden md:flex items-center text-sm ml-8">
            <span className="text-gray-400 mr-2">Organization:</span>
            <span className="font-medium text-gray-700">{formData?.companyName || 'Global Enterprises AG'}</span>
          </div>
          {/* User profile */}
          <div className="flex items-center ml-auto relative">
            <div className="mr-4 text-right hidden md:block">
              <div className="text-sm font-medium text-gray-700">{user?.name}</div>
              <div className="text-xs text-gray-500">{user?.role}</div>
            </div>
            <div className="relative group">
              <button
                className="flex items-center focus:outline-none"
                aria-label="User menu"
              >
                <div className="h-10 w-10 rounded-full overflow-hidden">
                  <img
                    src={user?.avatar || 'https://ui-avatars.com/api/?name=User&background=1A3A4A&color=fff'}
                    alt={user?.name || 'User'}
                    className="h-full w-full object-cover"
                  />
                </div>
              </button>

              {/* Dropdown menu */}
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                <div className="px-4 py-2 border-b border-gray-100">
                  <p className="text-sm font-medium text-gray-700">{user?.name}</p>
                  <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                </div>
                <a href="/dashboard/settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Account Settings
                </a>
                <button
                  onClick={logout}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          <Outlet />
        </main>
      </div>

      {/* Mobile Sidebar */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-black/50 z-50 md:hidden">
          <div className="absolute top-0 left-0 bottom-0 w-4/5 max-w-xs bg-primary animate-slide-right">
            <div className="p-4 flex justify-between items-center border-b border-white/10">
              <Logo variant="white" />
              <button
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-white p-2"
              >
                <X size={24} />
              </button>
            </div>

            <div className="px-2 py-4">
              <div className="flex items-center p-4 mb-4 border-b border-white/10">
                <div className="h-10 w-10 rounded-full overflow-hidden mr-3">
                  <img
                    src={user?.avatar || 'https://ui-avatars.com/api/?name=User&background=1A3A4A&color=fff'}
                    alt={user?.name || 'User'}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">{user?.name}</div>
                  <div className="text-xs text-gray-300">{user?.role}</div>
                </div>
              </div>

              <nav>
                <ul className="space-y-1">
                  {navItems.map((item, index) => (
                    'type' in item ? (
                      <li key={index} className="space-y-1">
                        <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                          {item.label}
                        </div>
                        <ul className="space-y-1">
                          {item.items?.map((subItem) => {
                            // Extract the use case ID from the href
                            const useCaseId = subItem.href.split('/').pop();

                            return (
                              <li key={subItem.label}>
                                <UseCaseNavLink
                                  to={subItem.href}
                                  useCaseId={useCaseId || ''}
                                  onClick={() => setIsMobileMenuOpen(false)}
                                  className={({ isActive }) =>
                                    `flex items-center px-4 py-2 text-sm rounded-md transition-colors ${
                                      isActive
                                        ? 'bg-white/10 text-white'
                                        : 'text-gray-300 hover:bg-white/5 hover:text-white'
                                    }`
                                  }
                                >
                                  <span className="mr-3">{subItem.icon}</span>
                                  <span>{subItem.label}</span>
                                </UseCaseNavLink>
                              </li>
                            );
                          })}
                        </ul>
                      </li>
                    ) : (
                      <li key={item.label}>
                        <NavLink
                          to={item.href}
                          end
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={({ isActive }) =>
                            `flex items-center px-4 py-3 rounded-md transition-colors ${
                              isActive
                                ? 'bg-white/10 text-white'
                                : 'text-gray-300 hover:bg-white/5 hover:text-white'
                            }`
                          }
                        >
                          <span className="mr-3">{item.icon}</span>
                          <span>{item.label}</span>
                        </NavLink>
                      </li>
                    )
                  ))}
                </ul>
              </nav>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/10">
              <button
                onClick={logout}
                className="flex items-center px-4 py-3 w-full text-gray-300 hover:text-white rounded-md transition-colors hover:bg-white/5"
              >
                <LogOut size={20} className="mr-3" />
                <span>Log Out</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}