import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * AuthGuard component to protect routes that require authentication.
 * Redirects to home page and shows login modal if user is not authenticated.
 */
export default function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isAuthLoading, openLoginModal } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Only proceed with auth check if authentication loading is complete
    if (!isAuthLoading) {
      if (!isAuthenticated) {
        // Save the current route before redirecting (for use case routes)
        if (location.pathname.includes('/dashboard/use-case/')) {
          console.log(`[AuthGuard] Saving intended route before redirect: ${location.pathname}`);
          localStorage.setItem('felix_intended_route', location.pathname);
        }

        // Redirect to home page
        navigate('/', { replace: true });

        // Show login modal after redirect
        setTimeout(() => {
          openLoginModal();
        }, 100);
      }
    }
  }, [isAuthenticated, isAuthLoading, navigate, openLoginModal, location.pathname]);

  // Show loading indicator while checking auth status
  if (isAuthLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Render children only if authenticated
  return isAuthenticated ? <>{children}</> : null;
}
