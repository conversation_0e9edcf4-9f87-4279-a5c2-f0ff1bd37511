import { useState, useEffect } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { X, LogIn } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../ui/Button';
import Logo from '../common/Logo';

export default function LoginModal() {
  const { isLoginModalOpen, closeLoginModal, login, isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Close modal if user becomes authenticated
  useEffect(() => {
    if (isAuthenticated && isLoginModalOpen) {
      closeLoginModal();
    }
  }, [isAuthenticated, isLoginModalOpen, closeLoginModal]);

  const handleMicrosoftLogin = async () => {
    setError('');
    setIsLoading(true);

    try {
      await login();
    } catch (err: any) {
      setError('<PERSON><PERSON> failed. Please try again.');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog.Root open={isLoginModalOpen} onOpenChange={closeLoginModal}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 animate-fade-in" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-w-md w-full bg-white rounded-lg shadow-xl p-6 z-50 animate-slide-up">
          <Dialog.Title className="sr-only">Login to your account</Dialog.Title>
          <div className="flex flex-col">
            <div className="flex justify-between items-center mb-6">
              <Logo />
              <Dialog.Close asChild>
                <button className="rounded-full p-1 text-gray-500 hover:bg-gray-100 focus:outline-none">
                  <X size={20} />
                </button>
              </Dialog.Close>
            </div>

            <h2 className="text-2xl font-semibold text-center mb-6">Welcome Back</h2>

            {error && (
              <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
                {error}
              </div>
            )}

            <div className="flex flex-col items-center">
              <p className="text-gray-600 mb-6 text-center">
                Sign in with your Microsoft account to access the application.
              </p>

              <Button
                onClick={handleMicrosoftLogin}
                className="w-full mb-4 flex items-center justify-center"
                isLoading={isLoading}
              >
                {!isLoading && <LogIn className="mr-2" size={18} />}
                Sign in with Microsoft
              </Button>

              <p className="text-center text-sm text-gray-600 mt-4">
                Don't have an account?{' '}
                <a href="#" className="text-accent hover:text-accent-600 font-medium">
                  Contact Sales
                </a>
              </p>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}