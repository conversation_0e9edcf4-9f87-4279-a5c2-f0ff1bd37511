import { useState } from 'react';
import { ArrowLeft, ArrowRight, BarChart3, Calculator, CircleDollarSign, Network, Settings } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import Button from '../ui/Button';

const useCases = [
  {
    id: 'analytical-review',
    icon: <BarChart3 className="h-10 w-10 text-accent" />,
    title: 'Analytical Review',
    description: 'Automated variance analysis comparing actuals, budgets & prior-year figures to identify significant deviations.'
  },
  {
    id: 'exchange-rate-review',
    icon: <CircleDollarSign className="h-10 w-10 text-accent" />,
    title: 'Exchange Rate Review',
    description: 'Automated validation of exchange rates for subsidiary financial statements with built-in consistency checks.'
  },
  {
    id: 'intercompany-reconciliation',
    icon: <Network className="h-10 w-10 text-accent" />,
    title: 'Intercompany Reconciliation',
    description: 'Smart detection and resolution of intercompany transaction discrepancies across multiple currencies.'
  },
  {
    id: 'rounding-issue-resolution',
    icon: <Calculator className="h-10 w-10 text-accent" />,
    title: 'Rounding Issue Resolution',
    description: 'Intelligent correction of rounding inconsistencies while maintaining cross-reference integrity.'
  },
  {
    id: 'custom-solutions',
    icon: <Settings className="h-10 w-10 text-accent" />,
    title: 'Custom Solutions',
    description: 'Tailored AI agents built to address your specific financial consolidation needs and requirements.'
  }
];

export default function UseCaseCarousel() {
  const [activeIndex, setActiveIndex] = useState(0);

  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? useCases.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === useCases.length - 1 ? 0 : prev + 1));
  };

  return (
    <section id="solutions" className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Felix will do for you</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Felix, an advanced AI agent developed by OrinkIA, offers specialized solutions for the most critical financial consolidation tasks.
          </p>
        </div>

        <div className="relative">
          {/* Desktop Carousel */}
          <div className="hidden md:grid grid-cols-5 gap-6">
            {useCases.map((useCase, index) => (
              <Card 
                key={useCase.id} 
                variant="bordered"
                className="transition-all h-full border-accent shadow-md"
              >
                <CardHeader className="flex flex-col items-center text-center">
                  <div className="p-3 rounded-full bg-accent-50 mb-4">
                    {useCase.icon}
                  </div>
                  <CardTitle className="text-xl">{useCase.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center p-6">
                  <p className="text-gray-600 line-clamp-4">{useCase.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Mobile Carousel */}
          <div className="md:hidden">
            <Card 
              key={useCases[activeIndex].id} 
              variant="bordered"
              className="border-accent shadow-md"
            >
              <CardHeader className="flex flex-col items-center text-center">
                <div className="p-3 rounded-full bg-accent-50 mb-4">
                  {useCases[activeIndex].icon}
                </div>
                <CardTitle className="text-xl">{useCases[activeIndex].title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center p-6">
                <p className="text-gray-600">{useCases[activeIndex].description}</p>
              </CardContent>
            </Card>

            <div className="flex justify-between items-center mt-6">
              <button 
                onClick={handlePrev}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                aria-label="Previous case"
              >
                <ArrowLeft size={20} className="text-primary" />
              </button>

              <div className="text-sm text-gray-500">
                {activeIndex + 1} of {useCases.length}
              </div>

              <button 
                onClick={handleNext}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                aria-label="Next case"
              >
                <ArrowRight size={20} className="text-primary" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}