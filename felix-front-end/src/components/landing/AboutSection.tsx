import { QuoteIcon } from 'lucide-react';

export default function AboutSection() {
  return (
    <section id="about" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">About OrinkIA</h2>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              OrinkIA SA is a Swiss fintech startup founded in Q1 2025 by an experienced team of finance and technology professionals with extensive backgrounds in corporate finance and artificial intelligence.
            </p>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              Our founders share the same frustration with inefficiencies of manual financial consolidation, having witnessed over hundreds of billions in avoidable consolidation errors throughout their careers in multinational corporations.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed">
              By combining deep financial expertise with cutting-edge AI technology, we've created Felix - a suite of specialized AI agents designed to streamline financial consolidation processes and eliminate common sources of errors.
            </p>
          </div>

          <div className="bg-gray-50 p-8 rounded-xl relative">
            <QuoteIcon className="h-12 w-12 text-accent absolute -top-6 -left-6 bg-white p-2 rounded-full" />
            <blockquote className="text-xl italic text-gray-700 mb-8 leading-relaxed">
              "Felix automatically fixes errors before they become problems, working like a self-driving system to keep our financial numbers accurate. It's transformed how our team approaches consolidation."
            </blockquote>
            <div className="flex items-center">
              <div className="h-14 w-14 rounded-full bg-primary text-white flex items-center justify-center text-xl font-bold mr-4">
                MS
              </div>
              <div>
                <div className="font-semibold text-primary">Marie Schneider</div>
                <div className="text-gray-600">CFO, Global Enterprises AG</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}