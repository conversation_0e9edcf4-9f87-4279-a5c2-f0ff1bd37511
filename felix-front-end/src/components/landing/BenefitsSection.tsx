import { CheckCircle, Upload, Brain, <PERSON><PERSON>heck, MessageSquare } from 'lucide-react';

const metrics = [
  { value: '95%', label: 'Manual reconciliation work eliminated' },
  { value: 'Hours', label: 'Reporting cycle reduction (from days)' },
  { value: '100%', label: 'Audit readiness' },
  { value: 'Bank-Grade', label: 'Security for your financial data' }
];

const processSteps = [
  { icon: <Upload className="h-8 w-8 text-accent" />, title: 'Data Input', description: 'Upload statements and balances' },
  { icon: <Brain className="h-8 w-8 text-accent" />, title: 'AI Processing', description: 'Automated analysis and reconciliation' },
  { icon: <FileCheck className="h-8 w-8 text-accent" />, title: 'Results Review', description: 'Inspect findings and confirmations' },
  { icon: <MessageSquare className="h-8 w-8 text-accent" />, title: 'Communication', description: 'Share results with stakeholders' }
];

export default function BenefitsSection() {
  return (
    <section className="py-16 md:py-24 bg-primary-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">The Felix Advantage</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our AI-powered platform delivers measurable results for finance teams.
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
          {metrics.map((metric, index) => (
            <div 
              key={index} 
              className="bg-white p-6 rounded-lg shadow-sm text-center border-t-4 border-accent"
            >
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">{metric.value}</div>
              <div className="text-gray-600">{metric.label}</div>
            </div>
          ))}
        </div>

        {/* Process Flow */}
        <div className="max-w-5xl mx-auto">
          <h3 className="text-2xl md:text-3xl font-bold text-center mb-12">How Felix works</h3>
          
          <div className="relative">
            {/* Desktop Process Flow */}
            <div className="hidden md:grid grid-cols-4 gap-2">
              {processSteps.map((step, index) => (
                <div key={index} className="text-center relative z-10">
                  <div className="bg-white h-20 w-20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm border border-gray-100">
                    {step.icon}
                  </div>
                  <h4 className="font-semibold text-primary text-lg mb-1">{step.title}</h4>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              ))}
              
              {/* Connection line */}
              <div className="absolute top-10 left-[10%] right-[10%] h-0.5 bg-gray-200">
                {/* Animated dot */}
                <div className="absolute top-1/2 -mt-2 left-0 h-4 w-4 rounded-full bg-accent animate-progress"></div>
              </div>
            </div>
            
            {/* Mobile Process Flow */}
            <div className="md:hidden space-y-8">
              {processSteps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className="bg-white h-16 w-16 rounded-full flex items-center justify-center mr-4 shadow-sm border border-gray-100 flex-shrink-0">
                    {step.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-primary text-lg">{step.title}</h4>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}