import { <PERSON><PERSON><PERSON>cle, CloudLightning, Lock } from 'lucide-react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '../ui/Card';

const features = [
  {
    icon: <CheckCircle className="h-12 w-12 text-accent" />,
    title: 'Precision Through Automation',
    description: 'Replace error-prone spreadsheets with AI-driven validation for 99.9% accuracy'
  },
  {
    icon: <CloudLightning className="h-12 w-12 text-accent" />,
    title: 'Agility Over Legacy',
    description: 'Cloud-native architecture enables real-time global collaboration across your finance team'
  },
  {
    icon: <Lock className="h-12 w-12 text-accent" />,
    title: 'Bank-Grade Security',
    description: 'Azure SOC 2 certified infrastructure with enterprise-level encryption and security controls'
  }
];

export default function ValueProposition() {
  return (
    <section id="benefits" className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Why Choose OrinkIA</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Our AI-powered platform delivers unmatched efficiency and accuracy for corporate financial consolidation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              variant="elevated"
              className="transform transition-all hover:-translate-y-1 border-t-4 border-accent"
            >
              <CardHeader className="flex flex-col items-center text-center">
                <div className="p-3 rounded-full bg-accent-50 mb-4">
                  {feature.icon}
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}