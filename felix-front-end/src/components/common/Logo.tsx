import { cn } from '../../utils/cn';

interface LogoProps {
  variant?: 'default' | 'white';
  showIcon?: boolean;
  className?: string;
}

export default function Logo({ variant = 'default', showIcon = true, className }: LogoProps) {
  return (
    <div className={cn('flex items-center', className)}>
      {showIcon && (
        <div className="mr-2">
          <svg width="32" height="32" viewBox="0 0 32 32" className="flex-shrink-0" xmlns="http://www.w3.org/2000/svg">
            <g fill="none">
              <circle cx="16" cy="16" r="3" fill="#70E08D" />
              <circle cx="10" cy="10" r="2" fill="#70E08D" />
              <circle cx="10" cy="22" r="2" fill="#70E08D" />
              <circle cx="22" cy="10" r="2" fill="#70E08D" />
              <circle cx="22" cy="22" r="2" fill="#70E08D" />
              <line x1="16" y1="13" x2="10" y2="10" stroke="#70E08D" strokeWidth="1" />
              <line x1="16" y1="19" x2="10" y2="22" stroke="#70E08D" strokeWidth="1" />
              <line x1="19" y1="16" x2="22" y2="10" stroke="#70E08D" strokeWidth="1" />
              <line x1="19" y1="16" x2="22" y2="22" stroke="#70E08D" strokeWidth="1" />
            </g>
          </svg>
        </div>
      )}
      <div className={cn(
        'text-2xl font-bold tracking-tight',
        variant === 'default' ? 'text-primary' : 'text-white'
      )}>
        ORINK<span className="text-accent">IA</span>
      </div>
    </div>
  );
}