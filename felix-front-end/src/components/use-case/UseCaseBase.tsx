import { useState, useEffect, useMemo, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLef<PERSON>, ArrowRight, CheckCircle, Loader } from 'lucide-react';
import Button from '../../components/ui/Button';
import { getUseCaseById } from '../../data/useCases';
import { icons } from '../../data/useCases';
import { UseCaseProvider, useUseCaseContext } from '../../contexts/UseCaseContext';
import requestService from '../../api/services/requestService';
import fileService from '../../api/services/fileService';
import { getUserId } from '../../auth/authService';
import {
  saveUseCaseId,
  saveUseCaseStep,
  getSavedUseCaseId,
  getSavedCurrentStep,
  getSavedStepPath,
  getSavedStepStatus,
  clearUseCaseData,
  clearAllUseCaseData
} from '../../utils/localStorage';

interface UseCaseBaseProps {
  id: string;
  children: ReactNode;
  basePath: string;
}

/**
 * UseCaseBase component handles the common workflow for different financial process use cases.
 * It manages step navigation, validation, and completion of the process.
 */
export default function UseCaseBase({ id, children, basePath }: UseCaseBaseProps) {
  // Log component mount for debugging
  console.log(`[UseCaseBase] Mounted with ID: ${id}, basePath: ${basePath}, location: ${window.location.pathname}`);
  const location = useLocation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const useCase = getUseCaseById(id || '');

  const IconComponent = useMemo(() =>
    useCase ? icons[useCase.icon as keyof typeof icons] : null,
    [useCase]
  );

  const steps = useMemo(() => [
    { id: 1, name: 'Data Input', path: '' },
    { id: 2, name: 'AI Processing', path: '/processing' },
    { id: 3, name: 'Results Review', path: '/results' },
    ...(useCase?.requiresCommunication ? [{ id: 4, name: 'Communication', path: '/communication' }] : []),
    { id: useCase?.requiresCommunication ? 5 : 4, name: 'End', path: '/end' }
  ], [useCase]);

  // Save current use case ID to storage when it changes
  useEffect(() => {
    if (id) {
      saveUseCaseId(id);
      console.log(`[UseCase] Set current use case ID: ${id}`);
    }
  }, [id]);

  // Check for saved session on initial load
  useEffect(() => {
    if (!id) return;

    // Get the current path relative to the base path
    const currentPathMatch = location.pathname.match(new RegExp(`${basePath}(.*)`));
    const currentRelativePath = currentPathMatch ? currentPathMatch[1] : '';

    // Check if we're at the root of a use case
    const isRootPath = currentRelativePath === '' || currentRelativePath === '/';
    const savedUseCaseId = getSavedUseCaseId();

    console.log(`[UseCase] Checking for saved state. Current ID: ${id}, Saved ID: ${savedUseCaseId}, Is root path: ${isRootPath}, Current path: ${location.pathname}`);

    // If we're on a specific step path (not root), save it to storage
    if (!isRootPath) {
      // Extract step number and status from the path
      let stepNumber = 1;
      let stepStatus = 'active';

      if (currentRelativePath === '/processing') {
        stepNumber = 2;
        stepStatus = 'processing';
      } else if (currentRelativePath === '/results') {
        stepNumber = 3;
        stepStatus = 'completed';
      } else if (currentRelativePath === '/communication') {
        stepNumber = 4;
        stepStatus = 'communication';
      } else if (currentRelativePath === '/end') {
        stepNumber = 5;
        stepStatus = 'finished';
      }

      // Save the current step to storage
      saveUseCaseStep(id, stepNumber, currentRelativePath, stepStatus);
      console.log(`[UseCase] Saved current path as step data: ${currentRelativePath}, step: ${stepNumber}, status: ${stepStatus}`);

      // No need to navigate since we're already on the correct path
      return;
    }

    // If we're at the root path, check if we have saved state to restore
    if (isRootPath && savedUseCaseId === id) {
      const savedStep = getSavedCurrentStep(id);
      const savedPath = getSavedStepPath(id);
      const savedStatus = getSavedStepStatus(id);

      console.log(`[UseCase] Found saved state for ${id}:`, { savedStep, savedPath, savedStatus });

      // If we have saved state and we're on the correct use case
      if (savedStep && savedPath) {
        console.log(`[UseCase] Navigating to saved step: ${savedPath}`);
        navigate(`${basePath}${savedPath}`, { replace: true });
      }
    }
  }, [id, navigate, location.pathname, basePath]);

  // Determine current step based on URL and save to storage
  useEffect(() => {
    if (!id) return;

    const path = location.pathname;
    let newStep = 1;
    let stepPath = '';
    let stepStatus = 'active';

    if (path.endsWith('/processing')) {
      newStep = 2;
      stepPath = '/processing';
      stepStatus = 'processing';
    } else if (path.endsWith('/results')) {
      newStep = 3;
      stepPath = '/results';
      stepStatus = 'completed';
    } else if (path.endsWith('/communication')) {
      newStep = 4;
      stepPath = '/communication';
      stepStatus = 'communication';
    } else if (path.endsWith('/end')) {
      newStep = 5;
      stepPath = '/end';
      stepStatus = 'finished';
    }

    setCurrentStep(newStep);

    // Save current step and path to storage
    saveUseCaseStep(id, newStep, stepPath, stepStatus);
    console.log(`[UseCase] Saved step data for ${id}:`, { newStep, stepPath, stepStatus });

  }, [location.pathname, id, basePath]);

  // Function to determine if communication step should be skipped
  const shouldSkipCommunication = useCase ? !useCase.requiresCommunication : false;

  // Clean up storage when component unmounts
  useEffect(() => {
    return () => {
      // Only clear if navigating away from use cases entirely
      if (!location.pathname.includes('/use-case/')) {
        console.log('[UseCase] Navigating away from use cases, clearing all use case data');
        clearAllUseCaseData();
      }
    };
  }, [location.pathname]);

  // Wrap the content with UseCaseProvider to share state across all steps
  return (
    <UseCaseProvider>
      <UseCaseContent
        id={id}
        useCase={useCase}
        currentStep={currentStep}
        IconComponent={IconComponent}
        steps={steps}
        navigate={navigate}
        shouldSkipCommunication={shouldSkipCommunication}
        basePath={basePath}
      >
        {children}
      </UseCaseContent>
    </UseCaseProvider>
  );
}

// Component that uses the context
function UseCaseContent({
  id,
  useCase,
  currentStep,
  IconComponent,
  steps,
  navigate,
  shouldSkipCommunication,
  basePath,
  children
}: {
  id: string | undefined,
  useCase: any,
  currentStep: number,
  IconComponent: any,
  steps: any[],
  navigate: any,
  shouldSkipCommunication: boolean,
  basePath: string,
  children: ReactNode
}) {
  const {
    setRequestId,
    files,
    isProcessing,
    setIsProcessing,
    processingError,
    setProcessingError,
    updateFileStatus
  } = useUseCaseContext();

  if (!useCase) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Use Case Not Found</h2>
          <p className="text-gray-600 mb-4">The specified use case does not exist.</p>
          <Button onClick={() => navigate('/dashboard')}>Return to Dashboard</Button>
        </div>
      </div>
    );
  }

  /**
   * Handles navigation to the next step in the process.
   * Validates current step completion and updates progress.
   */
  const handleNext = async () => {
    // If we're on the first step (data input), process the data
    if (currentStep === 1) {
      setIsProcessing(true);
      setProcessingError(null);

      try {
        // Check if there are valid files to process
        const validFiles = files.filter(file => file.status === 'valid' && file.file);

        if (validFiles.length === 0) {
          throw new Error('Please upload at least one valid file before processing.');
        }

        // Get the user ID for the request
        const userId = getUserId();
        if (!userId) {
          throw new Error('User ID not available. Please ensure you are logged in.');
        }

        // Step 1: Create a request in the database
        console.log('Creating request with user ID:', userId);
        const createResponse = await requestService.createRequest(userId);
        console.log('Request created successfully:', createResponse);

        // Check if the response has the expected structure
        if (!createResponse || !createResponse.data || !createResponse.data.id) {
          console.error('Invalid response structure:', createResponse);
          throw new Error('Failed to create request. Please try again.');
        }

        // Get the request ID from the response data field
        const createdRequestId = createResponse.data.id;
        console.log('Created request ID:', createdRequestId);

        // Save the request ID in context
        setRequestId(createdRequestId);

        // Step 2: Create file records and upload files
        let uploadErrors = 0;

        // Process files sequentially to avoid overwhelming the server
        for (const file of validFiles) {
          try {
            // Mark file as uploading
            updateFileStatus(file.id, 'uploading');

            // Get the file type for upload (pdf or csv)
            const fileType = file.file!.name.split('.').pop()?.toLowerCase() === 'pdf' ? 'pdf' : 'csv';

            // Step 2a: Create a file record in the database first
            console.log(`Creating file record for ${file.name}...`);
            // Use a temporary file path that will be updated after upload
            const tempFilePath = `uploads/${file.name}`;
            const fileRecordResponse = await fileService.createFileRecord(
              createdRequestId,
              file.name,
              tempFilePath
            );

            console.log('File record response:', fileRecordResponse);

            // Check if the file record response has the expected structure
            if (!fileRecordResponse || !fileRecordResponse.data || !fileRecordResponse.data.data) {
              console.error('Invalid file record response structure:', fileRecordResponse);
              throw new Error('Failed to create file record. Please try again.');
            }

            // Extract the file record data from the response
            // The structure is { data: { data: { id: '...', ... } } }
            const fileRecord = fileRecordResponse.data.data;
            console.log(`File record created successfully for ${file.name}. ID: ${fileRecord.id}`);

            // Step 2b: Upload the file to the server using the file ID from the record
            console.log(`Uploading ${file.name} as ${fileType} file...`);
            const uploadResponse = await fileService.uploadFile(
              file.file!,
              fileType,
              {
                fileId: fileRecord.id,
                requestId: createdRequestId,
                fileName: fileRecord.file_name,
                filePath: fileRecord.file_path
              }
            );

            // Check if the upload response has the expected structure
            if (!uploadResponse || !uploadResponse.data) {
              console.error('Invalid upload response structure:', uploadResponse);
              throw new Error('File upload failed. No response from server.');
            }

            // Log the response from the file upload API
            // The structure is { message_code: 0, message: 'File uploaded successfully', data: null }
            console.log('Upload response:', uploadResponse.data);

            // Check if the upload was successful based on the message_code
            if (uploadResponse.data.message_code !== 0) {
              throw new Error(`File upload failed: ${uploadResponse.data.message || 'Unknown error'}`);
            }

            // Use the file path from the file record since the upload response doesn't include it
            const filePath = fileRecord.file_path;
            console.log(`File uploaded successfully. Path: ${filePath}`);

            // Update file status to valid after successful upload and record creation
            updateFileStatus(file.id, 'valid');
          } catch (error) {
            console.error(`Error processing file ${file.name}:`, error);
            uploadErrors++;

            // Update file status to error
            updateFileStatus(
              file.id,
              'error',
              error instanceof Error ? error.message : 'Upload failed'
            );

            // Continue with other files
          }
        }

        // Check if any uploads failed
        if (uploadErrors > 0) {
          throw new Error(`${uploadErrors} file(s) failed to process. Please check the error messages and try again.`);
        }

        // Step 3: Update request status to PROCESSING
        console.log(`Updating request ${createdRequestId} status to PROCESSING...`);
        try {
          const updateResponse = await requestService.updateRequestStatus(createdRequestId, 'PROCESSING');
          console.log('Request status updated successfully:', updateResponse);
        } catch (error) {
          console.error('Error updating request status:', error);
          // Continue with navigation even if status update fails
          console.warn('Continuing despite status update error');
        }

        // Navigate to the next step after processing
        navigate(`${basePath}${steps[1].path}`);
      } catch (error) {
        console.error('Error processing data:', error);
        setProcessingError(error instanceof Error ? error.message : 'An unexpected error occurred');
      } finally {
        setIsProcessing(false);
      }
    } else {
      // For other steps, just navigate to the next step
      const nextStep = currentStep + 1;
      if (nextStep <= (shouldSkipCommunication ? 4 : 5)) {
        navigate(`${basePath}${steps[nextStep - 1].path}`);
      }
    }
  };

  /**
   * Handles navigation to the previous step or returns to dashboard.
   * Preserves step completion state.
   */
  const handleBack = () => {
    const prevStep = currentStep - 1;
    if (prevStep >= 1) {
      navigate(`${basePath}${steps[prevStep - 1].path}`);
    } else {
      // Clear storage when returning to dashboard
      if (id) {
        clearUseCaseData(id);
      }
      navigate('/dashboard');
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <div className="p-2 rounded-md bg-primary-50 mr-3">
            {IconComponent && <IconComponent className="h-6 w-6 text-primary" />}
          </div>
          <h1 className="text-2xl font-bold">{useCase.name}</h1>
        </div>
        <p className="text-gray-600 ml-11">{useCase.description}</p>

        {/* Step Navigation */}
        <nav className="mb-8" aria-label="Process Steps">
          <div className="flex items-center">
            {steps.map((step, index) => {
              // Skip communication step if not required, but always show END step
              if (step.id === 4 && shouldSkipCommunication && step.name !== 'End') return null;

              return (
                <div key={step.id} className="flex items-center relative">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      currentStep >= step.id ? 'bg-accent text-white' : 'bg-gray-200 text-gray-500'
                    }`}
                    aria-current={currentStep === step.id ? 'step' : undefined}
                  >
                    {currentStep > step.id ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="ml-3">
                    <div className={`text-sm font-medium ${currentStep === step.id ? 'text-primary' : 'text-gray-500'}`}>{step.name}</div>
                  </div>
                  {index < (shouldSkipCommunication ? 3 : 4) && (
                    <div className={`w-12 md:w-24 h-0.5 mx-2 ${currentStep > step.id ? 'bg-accent' : 'bg-gray-200'}`}></div>
                  )}
                </div>
              );
            })}
          </div>
        </nav>

        {/* Step Content */}
        <div className="mb-8">
          {children}
        </div>

        {/* Step Navigation Buttons */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={handleBack}
            leftIcon={<ArrowLeft size={16} />}
            disabled={isProcessing}
          >
            {currentStep === 1 ? 'Cancel' : 'Back'}
          </Button>

          {processingError && (
            <div className="text-red-600 text-sm flex-1 mx-4">
              {processingError}
            </div>
          )}

          {currentStep < (shouldSkipCommunication ? 4 : 5) && (
            <Button
              onClick={handleNext}
              size="lg"
              rightIcon={isProcessing ? <Loader className="animate-spin" size={16} /> : <ArrowRight size={16} />}
              disabled={isProcessing}
            >
              {currentStep === 1
                ? (isProcessing ? 'Processing...' : 'Process Data')
                : currentStep === (shouldSkipCommunication ? 3 : 4)
                  ? 'Complete Process'
                  : 'Next'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
