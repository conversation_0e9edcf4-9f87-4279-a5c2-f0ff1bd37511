import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * Higher-order component that adds route restoration functionality to use case components
 * This helps restore the correct route after a page refresh/reload
 */
export function withUseCaseRouteRestore<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  useCaseId: string
) {
  return function WithRouteRestore(props: P) {
    const location = useLocation();
    const navigate = useNavigate();
    const basePath = `/dashboard/use-case/${useCaseId}`;

    useEffect(() => {
      // On component mount, check if we're at the base path and need to restore a step
      if (location.pathname === basePath) {
        const savedStep = sessionStorage.getItem(`${useCaseId}_current_step`);
        if (savedStep && savedStep !== basePath) {
          console.log(`[withUseCaseRouteRestore] Restoring ${useCaseId} step: ${savedStep}`);
          navigate(savedStep, { replace: true });
        }
      }

      // Always save the current step when we're in a use case flow
      if (location.pathname.startsWith(basePath)) {
        console.log(`[withUseCaseRouteRestore] Saving current step for ${useCaseId}: ${location.pathname}`);
        sessionStorage.setItem(`${useCaseId}_current_step`, location.pathname);
        // Also update the main route restore system
        localStorage.setItem('felix_last_route', location.pathname);
      }
    }, [location.pathname, navigate, basePath]);

    return <WrappedComponent {...props} />;
  };
}

export default withUseCaseRouteRestore;