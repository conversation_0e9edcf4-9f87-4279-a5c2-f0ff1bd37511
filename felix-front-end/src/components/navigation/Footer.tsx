import { ArrowRight, Facebook, Linkedin, Twitter } from 'lucide-react';
import Logo from '../common/Logo';
import Button from '../ui/Button';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-primary text-white">
      {/* Primary Footer */}
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Column */}
          <div>
            <Logo variant="white" className="mb-4" />
            <p className="text-gray-300 mb-6">
              AI-powered financial consolidation for modern finance teams.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Solutions Column */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-4">Solutions</h4>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Analytical Review
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Exchange Rate Review
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Intercompany Reconciliation
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Rounding Issue Resolution
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Platform Overview
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Integration Options
                </a>
              </li>
            </ul>
          </div>

          {/* Resources Column */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-4">Resources</h4>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Documentation
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Case Studies
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Blog
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  FAQ
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-accent transition-colors">
                  Press
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Column */}
          <div>
            <h4 className="font-semibold text-white text-lg mb-4">Contact</h4>
            <ul className="space-y-3 mb-6">
              <li className="text-gray-300">
                <EMAIL>
              </li>
              <li className="text-gray-300">
                +41 22 123 45 67
              </li>
              <li className="text-gray-300">
                Canton de Vaud, Switzerland
              </li>
            </ul>
            <Button 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-primary"
              rightIcon={<ArrowRight size={16} />}
            >
              Contact Us
            </Button>
          </div>
        </div>
      </div>

      {/* Secondary Footer */}
      <div className="border-t border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} OrinkIA AI Solutions. All rights reserved.
            </div>
            <div className="flex items-center space-x-6">
              <a href="#" className="text-gray-400 hover:text-accent text-sm">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-accent text-sm">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-accent text-sm">
                Data Protection
              </a>
              <div className="flex items-center">
                <select className="bg-transparent text-gray-400 text-sm appearance-none pr-8 focus:outline-none cursor-pointer">
                  <option value="en">EN</option>
                  <option value="fr">FR</option>
                  <option value="de">DE</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}