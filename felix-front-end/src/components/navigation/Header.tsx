import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, Setting<PERSON>, User, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import Logo from '../common/Logo';
import Button from '../ui/Button';
import LoginModal from '../auth/LoginModal';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { useNavigate } from 'react-router-dom';

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isAuthenticated, openLoginModal } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Close mobile menu when location changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location]);

  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { label: 'Solutions', href: '/#solutions' },
    { label: 'Benefits', href: '/#benefits' },
    { label: 'About Us', href: '/#about' },
    { label: 'Contact', href: '/#contact' }
  ];

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-sm py-3' : 'bg-transparent py-5'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="z-10">
            <Logo variant={isScrolled || isMobileMenuOpen ? 'default' : 'white'} />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <ul className="flex items-center space-x-6">
              {navItems.map((item) => (
                <li key={item.label}>
                  <a 
                    href={item.href} 
                    className={`text-base font-medium transition-colors ${
                      isScrolled ? 'text-primary hover:text-accent' : 'text-white hover:text-accent'
                    }`}
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>

            {isAuthenticated ? (
              <Link to="/dashboard">
                <Button size="md">Dashboard</Button>
              </Link>
            ) : (
              <Button size="md" onClick={openLoginModal}>Login</Button>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden z-10"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? (
              <X className={isScrolled ? 'text-primary' : 'text-white'} size={24} />
            ) : (
              <Menu className={isScrolled ? 'text-primary' : 'text-white'} size={24} />
            )}
          </button>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden fixed inset-0 bg-white z-[40] animate-fade-in">
              <div className="container mx-auto px-4 py-20">
                <ul className="flex flex-col space-y-6">
                  {navItems.map((item) => (
                    <li key={item.label}>
                      <a 
                        href={item.href} 
                        className="text-xl font-medium text-primary hover:text-accent"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.label}
                      </a>
                    </li>
                  ))}
                  <li>
                    {isAuthenticated ? (
                      <Link to="/dashboard">
                        <Button size="lg" className="w-full">Dashboard</Button>
                      </Link>
                    ) : (
                      <Button 
                        size="lg" 
                        className="w-full"
                        onClick={() => {
                          setIsMobileMenuOpen(false);
                          openLoginModal();
                        }}
                      >
                        Login
                      </Button>
                    )}
                  </li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal />
    </header>
  );
}