import { useState, useCallback } from 'react';
import { api } from '../services/apiService';

interface UseApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function useApi<T>(options: UseApiOptions = {}) {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const execute = useCallback(
    async (
      method: 'get' | 'post' | 'put' | 'delete',
      endpoint: string,
      payload?: any
    ) => {
      setIsLoading(true);
      setError(null);

      try {
        let result;

        switch (method) {
          case 'get':
            result = await api.get<T>(endpoint);
            break;
          case 'post':
            result = await api.post<T>(endpoint, payload);
            break;
          case 'put':
            result = await api.put<T>(endpoint, payload);
            break;
          case 'delete':
            result = await api.delete<T>(endpoint);
            break;
          default:
            throw new Error(`Unsupported method: ${method}`);
        }

        setData(result);
        options.onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [options]
  );

  const get = useCallback(
    (endpoint: string) => execute('get', endpoint),
    [execute]
  );

  const post = useCallback(
    (endpoint: string, payload: any) => execute('post', endpoint, payload),
    [execute]
  );

  const put = useCallback(
    (endpoint: string, payload: any) => execute('put', endpoint, payload),
    [execute]
  );

  const del = useCallback(
    (endpoint: string) => execute('delete', endpoint),
    [execute]
  );

  return {
    data,
    error,
    isLoading,
    get,
    post,
    put,
    delete: del,
  };
}
