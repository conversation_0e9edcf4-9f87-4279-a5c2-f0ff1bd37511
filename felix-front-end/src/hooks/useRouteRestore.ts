import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ROUTE_STORAGE_KEY = 'felix_last_route';
const INTENDED_ROUTE_KEY = 'felix_intended_route';

/**
 * Hook to preserve and restore route on page refresh
 * This helps maintain the user's position in multi-step flows when refreshing the page
 */
export function useRouteRestore() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isAuthLoading } = useAuth();

  // Save current route to localStorage when it changes
  useEffect(() => {
    // Only save routes that should be restored (use case routes)
    if (location.pathname.includes('/dashboard/use-case/')) {
      localStorage.setItem(ROUTE_STORAGE_KEY, location.pathname);
    }
  }, [location.pathname]);

  // Check for saved route on initial load - but only after auth check is complete
  useEffect(() => {
    // Don't restore routes while authentication is still loading
    if (isAuthLoading) {
      return;
    }

    const savedRoute = localStorage.getItem(ROUTE_STORAGE_KEY);
    const intendedRoute = localStorage.getItem(INTENDED_ROUTE_KEY);

    // Handle different restoration scenarios
    if (isAuthenticated) {
      // Scenario 1: User is at root route - restore intended route first, then saved route
      if (location.pathname === '/') {
        if (intendedRoute && intendedRoute.includes('/dashboard/use-case/')) {
          console.log(`[RouteRestore] Restoring intended route after auth: ${intendedRoute}`);
          localStorage.removeItem(INTENDED_ROUTE_KEY); // Clear after use
          navigate(intendedRoute, { replace: true });
        } else if (savedRoute && savedRoute.includes('/dashboard/use-case/')) {
          console.log(`[RouteRestore] Restoring saved route from root: ${savedRoute}`);
          navigate(savedRoute, { replace: true });
        }
      }
      // Scenario 2: User refreshed on a use case route - validate and maintain current route
      else if (location.pathname.includes('/dashboard/use-case/')) {
        console.log(`[RouteRestore] User refreshed on use case route: ${location.pathname}`);
        // Update saved route to current location since they're already on a valid use case route
        localStorage.setItem(ROUTE_STORAGE_KEY, location.pathname);
      }
      // Scenario 3: User is on dashboard but not root - don't interfere
      else if (location.pathname.startsWith('/dashboard/')) {
        console.log(`[RouteRestore] User on dashboard route: ${location.pathname}`);
        // Don't restore use case routes when user is intentionally on other dashboard pages
      }
    } else if (!isAuthenticated && (savedRoute || intendedRoute)) {
      // Clear saved routes if user is not authenticated
      console.log(`[RouteRestore] Clearing saved routes - user not authenticated`);
      localStorage.removeItem(ROUTE_STORAGE_KEY);
      localStorage.removeItem(INTENDED_ROUTE_KEY);
    }
  }, [navigate, location.pathname, isAuthenticated, isAuthLoading]);
}

export default useRouteRestore;