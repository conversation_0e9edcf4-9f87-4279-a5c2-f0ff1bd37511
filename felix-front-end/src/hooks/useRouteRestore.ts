import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ROUTE_STORAGE_KEY = 'felix_last_route';

/**
 * Hook to preserve and restore route on page refresh
 * This helps maintain the user's position in multi-step flows when refreshing the page
 */
export function useRouteRestore() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isAuthLoading } = useAuth();

  // Save current route to localStorage when it changes
  useEffect(() => {
    // Only save routes that should be restored (use case routes)
    if (location.pathname.includes('/dashboard/use-case/')) {
      localStorage.setItem(ROUTE_STORAGE_KEY, location.pathname);
    }
  }, [location.pathname]);

  // Check for saved route on initial load - but only after auth check is complete
  useEffect(() => {
    // Don't restore routes while authentication is still loading
    if (isAuthLoading) {
      return;
    }

    const savedRoute = localStorage.getItem(ROUTE_STORAGE_KEY);

    // Only restore routes if user is authenticated and we're at the root route
    if (
      isAuthenticated &&
      location.pathname === '/' &&
      savedRoute &&
      savedRoute.includes('/dashboard/use-case/')
    ) {
      console.log(`[RouteRestore] Restoring route: ${savedRoute}`);
      navigate(savedRoute, { replace: true });
    } else if (!isAuthenticated && savedRoute) {
      // Clear saved route if user is not authenticated
      console.log(`[RouteRestore] Clearing saved route - user not authenticated`);
      localStorage.removeItem(ROUTE_STORAGE_KEY);
    }
  }, [navigate, location.pathname, isAuthenticated, isAuthLoading]);
}

export default useRouteRestore;