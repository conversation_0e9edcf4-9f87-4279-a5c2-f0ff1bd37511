import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Card, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { AlertTriangle, Upload, X, Plus, Edit, Trash2, Loader2 } from 'lucide-react';
import subsidiaryService, { Subsidiary, SubsidiaryCreate, SubsidiaryUpdate } from '../../api/services/subsidiaryService';
import { getEnv } from '../../utils/runtimeEnv';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  emailSignature: string;
  // Company Information
  companyName: string;
  tradingName: string;
  streetAddress: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  industry: string;
  employeeCount: string;
  subsidiaryCount: string;
  companyLogo: string;
  companyWebsite: string;
  department: string;
  position: string;
  language: string;
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  materialityThreshold: string;
  defaultCurrency: string;
  emailReminderFrequency: string;
  defaultView: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  enableTwoFactor: boolean;
}

interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

const initialFormData: FormData = {
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  emailSignature: '',
  // Company Information
  companyName: '',
  tradingName: '',
  streetAddress: '',
  city: '',
  state: '',
  postalCode: '',
  country: '',
  industry: '',
  employeeCount: '',
  subsidiaryCount: '',
  companyLogo: '',
  companyWebsite: '',
  department: '',
  position: '',
  language: 'en',
  timezone: 'CET',
  dateFormat: 'dd/mm/yyyy',
  numberFormat: '1,234.56',
  materialityThreshold: '5%',
  defaultCurrency: 'CHF',
  emailReminderFrequency: 'daily',
  defaultView: 'summary',
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
  enableTwoFactor: false
};

function FormSection({ title, description, children }: FormSectionProps) {
  return (
    <div className="mb-8">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
      </div>
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
}

function SubsidiaryManagement() {
  const [subsidiaries, setSubsidiaries] = useState<Subsidiary[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selected, setSelected] = useState<Subsidiary | null>(null);
  const [form, setForm] = useState<Omit<SubsidiaryCreate, 'tenant_id'> & Partial<Pick<Subsidiary, 'id'>>>({
    email: '',
    contact_name: '',
    entity_name: '',
    entity_code: '',
  });
  const [submitLoading, setSubmitLoading] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchSubsidiaries = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await subsidiaryService.getSubsidiaries({ limit: 100 });
      setSubsidiaries(res.data || []);
    } catch (e: any) {
      setError(e.message || 'Failed to load subsidiaries');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSubsidiaries();
  }, []);

  const openCreate = () => {
    setModalMode('create');
    setForm({ email: '', contact_name: '', entity_name: '', entity_code: '' });
    setSelected(null);
    setShowModal(true);
  };
  const openEdit = (sub: Subsidiary) => {
    setModalMode('edit');
    setForm({
      id: sub.id,
      email: sub.email,
      contact_name: sub.contact_name,
      entity_name: sub.entity_name,
      entity_code: sub.entity_code,
    });
    setSelected(sub);
    setShowModal(true);
  };
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm(f => ({ ...f, [e.target.name]: e.target.value }));
  };
  const handleSubmit = async (e: React.FormEvent) => {
    console.log('handleSubmit', form);
    e.preventDefault();
    setSubmitLoading(true);
    try {
      const tenant_id = getEnv('TENANT_ID');
      if (!tenant_id) throw new Error('Tenant ID is not set in environment variables');
      let res;
      if (modalMode === 'create') {
        res = await subsidiaryService.createSubsidiary({ ...form, tenant_id } as SubsidiaryCreate);
        console.log('subsidiary create res', res);
      } else if (modalMode === 'edit' && selected?.id) {
        res = await subsidiaryService.updateSubsidiary(selected.id, { ...form, tenant_id } as SubsidiaryUpdate);
        console.log('subsidiary update res', res);
      }
      setShowModal(false);
      await fetchSubsidiaries();
    } catch (e: any) {
      console.error('Subsidiary create/update error:', e);
      setError(e.message || 'Failed to save subsidiary');
    } finally {
      setSubmitLoading(false);
    }
  };
  const handleDelete = async () => {
    if (!deleteId) return;
    setDeleteLoading(true);
    try {
      const res = await subsidiaryService.deleteSubsidiary(deleteId);
      console.log('subsidiary delete res', res);
      setDeleteId(null);
      await fetchSubsidiaries();
    } catch (e: any) {
      console.error('Subsidiary delete error:', e);
      setError(e.message || 'Failed to delete subsidiary');
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Subsidiaries</h2>
        <Button onClick={openCreate} variant="outline" size="sm" leftIcon={<Plus size={16} />}>Add Subsidiary</Button>
      </div>
      {loading ? (
        <div className="flex justify-center py-12"><Loader2 className="animate-spin h-6 w-6 text-primary" /></div>
      ) : error ? (
        <div className="text-red-600 text-center py-4">{error}</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border rounded-lg">
            <thead>
              <tr className="bg-gray-50 border-b">
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Entity Name</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Entity Code</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Contact Name</th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody>
              {subsidiaries.length === 0 ? (
                <tr><td colSpan={5} className="text-center text-gray-400 py-8">No subsidiaries found.</td></tr>
              ) : subsidiaries.map(sub => (
                <tr key={sub.id} className="hover:bg-gray-50 border-b">
                  <td className="px-4 py-2 text-sm text-gray-700">{sub.entity_name}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">{sub.entity_code}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">{sub.contact_name}</td>
                  <td className="px-4 py-2 text-sm text-gray-700">{sub.email}</td>
                  <td className="px-4 py-2 text-right">
                    <Button variant="ghost" size="sm" onClick={() => openEdit(sub)}><Edit size={16} /></Button>
                    <Button variant="ghost" size="sm" onClick={() => setDeleteId(sub.id!)}><Trash2 size={16} className="text-red-500" /></Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {/* Modal for create/edit */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            {(submitLoading || deleteLoading) && (
              <div className="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center z-10">
                <Loader2 className="animate-spin h-8 w-8 text-primary" />
              </div>
            )}
            <button className="absolute top-2 right-2 text-gray-400 hover:text-gray-600" onClick={() => setShowModal(false)}><X size={20} /></button>
            <h3 className="text-lg font-semibold mb-4">{modalMode === 'create' ? 'Add Subsidiary' : 'Edit Subsidiary'}</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Entity Name</label>
                <input name="entity_name" className="input w-full" value={form.entity_name} onChange={handleFormChange} required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Entity Code</label>
                <input name="entity_code" className="input w-full" value={form.entity_code} onChange={handleFormChange} required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                <input name="contact_name" className="input w-full" value={form.contact_name} onChange={handleFormChange} required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input name="email" type="email" className="input w-full" value={form.email} onChange={handleFormChange} required />
              </div>
              <div className="flex justify-end space-x-2 pt-2">
                <Button type="button" variant="outline" onClick={() => setShowModal(false)}>Cancel</Button>
                <Button type="submit" isLoading={submitLoading}>{modalMode === 'create' ? 'Create' : 'Save'}</Button>
              </div>
            </form>
          </div>
        </div>
      )}
      {/* Delete confirmation */}
      {deleteId && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-sm p-6 relative">
            {deleteLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center z-10">
                <Loader2 className="animate-spin h-8 w-8 text-primary" />
              </div>
            )}
            <h3 className="text-lg font-semibold mb-4 text-red-600">Delete Subsidiary</h3>
            <p className="mb-6 text-gray-700">Are you sure you want to delete this subsidiary?</p>
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setDeleteId(null)}>Cancel</Button>
              <Button type="button" variant="outline" className="text-red-600 border-red-200 hover:bg-red-50" isLoading={deleteLoading} onClick={handleDelete}>Delete</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default function Settings() {
  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<FormData> & { submit?: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        firstName: user.name.split(' ')[0],
        lastName: user.name.split(' ')[1] || '',
        email: user.email,
        position: user.role
      }));
    }
  }, [user]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
    // Clear error when user starts typing
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: Partial<FormData> = {};

    // Profile validation
    if (activeTab === 'profile') {
      if (!formData.firstName) newErrors.firstName = 'First name is required';
      if (!formData.lastName) newErrors.lastName = 'Last name is required';
      if (!formData.email) newErrors.email = 'Email is required';
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Invalid email format';
      if (!formData.phone) newErrors.phone = 'Phone number is required';
    }

    // Security validation
    if (activeTab === 'security' && (formData.newPassword || formData.confirmPassword)) {
      if (!formData.currentPassword) newErrors.currentPassword = 'Current password is required';
      if (!formData.newPassword) newErrors.newPassword = 'New password is required';
      if (formData.newPassword.length < 8) newErrors.newPassword = 'Password must be at least 8 characters';
      if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSuccessMessage('');

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccessMessage('Settings updated successfully');

      // Clear sensitive fields
      if (activeTab === 'security') {
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }));
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        submit: 'Failed to update settings. Please try again.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Settings</h1>
        <p className="text-gray-600">Manage your account and preferences</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'profile'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('profile')}
        >
          My Profile
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'company'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('company')}
        >
          Company
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'users'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('users')}
        >
          Company Contacts
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'subsidiaries'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('subsidiaries')}
        >
          Subsidiary Management
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'notifications'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('notifications')}
        >
          Notifications
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'preferences'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('preferences')}
        >
          Preferences
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'security'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('security')}
        >
          Security
        </button>
      </div>

      {activeTab === 'profile' && (
        <Card variant="bordered">
          <CardContent className="p-6">
            <form onSubmit={handleSubmit}>
              <FormSection title="Personal Information" description="Update your account details">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      First Name
                    </label>
                    <input
                      name="firstName"
                      type="text"
                      id="firstName"
                      className={`input ${errors.firstName ? 'border-red-500' : ''}`}
                      value={formData.firstName}
                      onChange={handleInputChange}
                    />
                    {errors.firstName && (
                      <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name
                    </label>
                    <input
                      name="lastName"
                      type="text"
                      id="lastName"
                      className={`input ${errors.lastName ? 'border-red-500' : ''}`}
                      value={formData.lastName}
                      onChange={handleInputChange}
                    />
                    {errors.lastName && (
                      <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <input
                      name="email"
                      type="email"
                      id="email"
                      className={`input ${errors.email ? 'border-red-500' : ''}`}
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      name="phone"
                      type="tel"
                      id="phone"
                      className={`input ${errors.phone ? 'border-red-500' : ''}`}
                      value={formData.phone}
                      onChange={handleInputChange}
                    />
                    {errors.phone && (
                      <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                    )}
                  </div>
                </div>
              </FormSection>

              <FormSection title="Company Information">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                      Department
                    </label>
                    <input
                      name="department"
                      type="text"
                      id="department"
                      className="input"
                      value={formData.department}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                      Position
                    </label>
                    <input
                      name="position"
                      type="text"
                      id="position"
                      className="input"
                      value={formData.position}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </FormSection>

              <FormSection title="Profile Picture">
                <div className="flex items-center">
                  <div className="h-20 w-20 rounded-full overflow-hidden mr-6">
                    <img
                      src="https://ui-avatars.com/api/?name=John+Doe&background=1A3A4A&color=fff&size=80"
                      alt="Profile"
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="mb-2"
                    >
                      Upload Photo
                    </Button>
                    <p className="text-xs text-gray-500">JPEG, PNG or GIF, Max 2MB</p>
                  </div>
                </div>
              </FormSection>

              <FormSection title="Email Signature">
                <div>
                  <label htmlFor="emailSignature" className="block text-sm font-medium text-gray-700 mb-1">
                    Signature
                  </label>
                  <textarea
                    name="emailSignature"
                    id="emailSignature"
                    rows={4}
                    className="input"
                    placeholder="Enter your email signature..."
                    value={formData.emailSignature}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-gray-500 mt-1">This signature will be added to all your outgoing emails.</p>
                </div>
              </FormSection>

              {successMessage && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-800 text-sm">{successMessage}</p>
                </div>
              )}

              {errors.submit && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-start">
                  <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-red-800 text-sm">{errors.submit}</p>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setFormData(initialFormData);
                    setErrors({});
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  isLoading={isSubmitting}
                >
                  Save Changes
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {activeTab === 'company' && (
        <Card variant="bordered">
          <CardContent className="p-6">
            <form onSubmit={handleSubmit}>
              <FormSection title="Company Logo" description="Upload your company logo for branding">
                <div className="flex items-start space-x-6">
                  <div className="w-40 h-40 relative rounded-lg border-2 border-dashed border-gray-300 p-2 flex items-center justify-center">
                    {formData.companyLogo ? (
                      <>
                        <img
                          src={formData.companyLogo}
                          alt="Company Logo"
                          className="max-w-full max-h-full object-contain"
                        />
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, companyLogo: '' }))}
                          className="absolute -top-2 -right-2 bg-red-100 text-red-600 rounded-full p-1 hover:bg-red-200"
                        >
                          <X size={16} />
                        </button>
                      </>
                    ) : (
                      <div
                        className="w-full h-full flex flex-col items-center justify-center cursor-pointer"
                        onClick={() => document.getElementById('logo-upload')?.click()}
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.currentTarget.classList.add('border-accent');
                        }}
                        onDragLeave={(e) => {
                          e.currentTarget.classList.remove('border-accent');
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          e.currentTarget.classList.remove('border-accent');
                          const file = e.dataTransfer.files[0];
                          if (file && file.type.startsWith('image/')) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              setFormData(prev => ({
                                ...prev,
                                companyLogo: e.target?.result as string
                              }));
                            };
                            reader.readAsDataURL(file);
                          }
                        }}
                      >
                        <Upload className="h-8 w-8 text-gray-400 mb-2" />
                        <p className="text-sm text-gray-500">Drag & drop or click to upload</p>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <input
                      type="file"
                      id="logo-upload"
                      className="hidden"
                      accept="image/png,image/jpeg,image/svg+xml"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (e) => {
                            setFormData(prev => ({
                              ...prev,
                              companyLogo: e.target?.result as string
                            }));
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                    />
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Logo Requirements</h4>
                    <ul className="text-sm text-gray-500 space-y-1">
                      <li>• Supported formats: PNG, JPG, SVG</li>
                      <li>• Maximum file size: 2MB</li>
                      <li>• Minimum dimensions: 200x200 pixels</li>
                      <li>• Recommended: 400x400 pixels</li>
                    </ul>
                  </div>
                </div>
              </FormSection>

              <FormSection title="Company Details" description="Enter your company's basic information">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-1">
                      Legal Company Name
                    </label>
                    <input
                      name="companyName"
                      type="text"
                      id="companyName"
                      className="input"
                      value={formData.companyName}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="tradingName" className="block text-sm font-medium text-gray-700 mb-1">
                      Trading Name
                      <span className="text-gray-500 text-xs ml-1">(if different)</span>
                    </label>
                    <input
                      name="tradingName"
                      type="text"
                      id="tradingName"
                      className="input"
                      value={formData.tradingName}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </FormSection>

              <FormSection title="Company Address">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label htmlFor="streetAddress" className="block text-sm font-medium text-gray-700 mb-1">
                      Street Address
                    </label>
                    <input
                      name="streetAddress"
                      type="text"
                      id="streetAddress"
                      className="input"
                      value={formData.streetAddress}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                      City
                    </label>
                    <input
                      name="city"
                      type="text"
                      id="city"
                      className="input"
                      value={formData.city}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                      State/Province
                    </label>
                    <input
                      name="state"
                      type="text"
                      id="state"
                      className="input"
                      value={formData.state}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code
                    </label>
                    <input
                      name="postalCode"
                      type="text"
                      id="postalCode"
                      className="input"
                      value={formData.postalCode}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                      Country
                    </label>
                    <select
                      name="country"
                      id="country"
                      className="input"
                      value={formData.country}
                      onChange={handleInputChange}
                    >
                      <option value="">Select a country</option>
                      <option value="CH">Switzerland</option>
                      <option value="DE">Germany</option>
                      <option value="FR">France</option>
                      <option value="IT">Italy</option>
                      <option value="GB">United Kingdom</option>
                    </select>
                  </div>
                </div>
              </FormSection>

              <FormSection title="Business Information">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                      Industry/Sector
                    </label>
                    <select
                      name="industry"
                      id="industry"
                      className="input"
                      value={formData.industry}
                      onChange={handleInputChange}
                    >
                      <option value="">Select an industry</option>
                      <option value="finance">Financial Services</option>
                      <option value="technology">Technology</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="retail">Retail</option>
                      <option value="healthcare">Healthcare</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="employeeCount" className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Employees
                    </label>
                    <select
                      name="employeeCount"
                      id="employeeCount"
                      className="input"
                      value={formData.employeeCount}
                      onChange={handleInputChange}
                    >
                      <option value="">Select range</option>
                      <option value="1-50">1-50</option>
                      <option value="51-200">51-200</option>
                      <option value="201-500">201-500</option>
                      <option value="501-1000">501-1,000</option>
                      <option value="1001+">1,000+</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="subsidiaryCount" className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Subsidiaries
                    </label>
                    <input
                      name="subsidiaryCount"
                      type="number"
                      id="subsidiaryCount"
                      className="input"
                      min="0"
                      value={formData.subsidiaryCount}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <label htmlFor="companyWebsite" className="block text-sm font-medium text-gray-700 mb-1">
                      Company Website
                    </label>
                    <input
                      name="companyWebsite"
                      type="url"
                      id="companyWebsite"
                      className="input"
                      placeholder="https://"
                      value={formData.companyWebsite}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </FormSection>

              {successMessage && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-800 text-sm">{successMessage}</p>
                </div>
              )}

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (confirm('Are you sure you want to discard your changes?')) {
                      setFormData(initialFormData);
                      setErrors({});
                    }
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  isLoading={isSubmitting}
                >
                  Save Changes
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {activeTab === 'notifications' && (
        <Card variant="bordered">
          <CardContent className="p-6">
            <FormSection title="Email Notifications" description="Manage when you'll receive emails">
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="processComplete"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      defaultChecked
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="processComplete" className="font-medium text-gray-700">Process completion</label>
                    <p className="text-gray-500">Get notified when a process completes or encounters issues.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="intercompanyResponses"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      defaultChecked
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="intercompanyResponses" className="font-medium text-gray-700">Intercompany responses</label>
                    <p className="text-gray-500">Receive email when entities respond to intercompany reconciliation requests.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="dailySummary"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="dailySummary" className="font-medium text-gray-700">Daily summary</label>
                    <p className="text-gray-500">Get a daily summary of all active processes and pending tasks.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="weeklyReport"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      defaultChecked
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="weeklyReport" className="font-medium text-gray-700">Weekly report</label>
                    <p className="text-gray-500">Receive a weekly report with performance metrics and statistics.</p>
                  </div>
                </div>
              </div>
            </FormSection>

            <FormSection title="System Notifications" description="Control in-app notifications">
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="newIssuesDetected"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      defaultChecked
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="newIssuesDetected" className="font-medium text-gray-700">New issues detected</label>
                    <p className="text-gray-500">Get notified when Felix detects new issues in a process.</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="reminderNotifications"
                      type="checkbox"
                      className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      defaultChecked
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="reminderNotifications" className="font-medium text-gray-700">Reminder notifications</label>
                    <p className="text-gray-500">Receive reminders for pending tasks and deadlines.</p>
                  </div>
                </div>
              </div>
            </FormSection>
          </CardContent>
        </Card>
      )}

      {activeTab === 'subsidiaries' && (
        <Card variant="bordered">
          <CardContent className="p-6">
            <SubsidiaryManagement />
          </CardContent>
        </Card>
      )}
    </div>
  );
}