import { Routes, Route } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';
import UseCaseBase from '../../../components/use-case/UseCaseBase';
import DataInputStep from '../steps/DataInputStep';
import ProcessingStep from '../steps/ProcessingStep';
import ResultsStep from '../steps/ResultsStep';
import EndStep from '../steps/EndStep';
import { getUseCaseById } from '../../../data/useCases';
import { useState } from 'react';
import withUseCaseRouteRestore from '../../../components/use-case/withUseCaseRouteRestore';

/**
 * RoundingIssueResolution component handles the workflow for the Rounding Issue Resolution use case.
 */
function RoundingIssueResolution() {
  const id = 'rounding-issue-resolution';
  const useCase = getUseCaseById(id);
  const [showHelp, setShowHelp] = useState(false);
  const basePath = `/dashboard/use-case/${id}`;

  if (!useCase) {
    return <div>Use case not found</div>;
  }

  return (
    <UseCaseBase id={id} basePath={basePath}>
      {/* Help content specific to Rounding Issue Resolution */}
      <div className="mt-4 ml-11 mb-8">
        <button
          onClick={() => setShowHelp(!showHelp)}
          aria-expanded={showHelp}
          aria-controls="help-content"
          className="text-accent hover:text-accent-600 font-medium flex items-center"
        >
          Learn more {showHelp ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
        </button>

        {showHelp && (
          <div id="help-content" className="mt-1 bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-700 mb-6">
              This represents the final quality check in the consolidation process, occurring just before financial statement publication.
              It ensures mathematical accuracy and consistency across all financial statements while maintaining IFRS compliance.
            </p>

            <div className="space-y-6">
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Focus</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Correcting inconsistencies between related financial tables</li>
                  <li>Maintaining mathematical accuracy in totals and variations</li>
                  <li>Preserving cross-reference integrity between financial statements</li>
                  <li>Documenting all rounding adjustments with clear explanations</li>
                </ul>
              </div>

              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Data Input</h4>
                <div className="space-y-2 text-gray-700">
                  <p className="font-medium mb-1">Source Documents:</p>
                  <ol className="list-decimal pl-5 space-y-1">
                    <li>Financial statements in IFRS format</li>
                    <li>Tables requiring rounding corrections</li>
                    <li>Financial statements template specifying where figure adjustments are not permitted</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Step Content */}
      <Routes>
        <Route index element={<DataInputStep useCase={useCase} />} />
        <Route path="/processing" element={<ProcessingStep useCase={useCase} />} />
        <Route path="/results" element={<ResultsStep useCase={useCase} />} />
        <Route path="/end" element={
          <div className="max-w-4xl mx-auto">
            <EndStep useCase={useCase} />
          </div>
        } />
      </Routes>
    </UseCaseBase>
  );
}

// Wrap component with route restore HOC
export default withUseCaseRouteRestore(RoundingIssueResolution, 'rounding-issue-resolution');
