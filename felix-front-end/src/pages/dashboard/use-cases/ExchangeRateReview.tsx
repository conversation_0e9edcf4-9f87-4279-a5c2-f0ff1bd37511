import { Routes, Route } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';
import UseCaseBase from '../../../components/use-case/UseCaseBase';
import DataInputStep from '../steps/DataInputStep';
import ProcessingStep from '../steps/ProcessingStep';
import ResultsStep from '../steps/ResultsStep';
import EndStep from '../steps/EndStep';
import { getUseCaseById } from '../../../data/useCases';
import { useState } from 'react';
import withUseCaseRouteRestore from '../../../components/use-case/withUseCaseRouteRestore';

/**
 * ExchangeRateReview component handles the workflow for the Exchange Rate Review use case.
 */
function ExchangeRateReview() {
  const id = 'exchange-rate-review';
  const useCase = getUseCaseById(id);
  const [showHelp, setShowHelp] = useState(false);
  const basePath = `/dashboard/use-case/${id}`;

  if (!useCase) {
    return <div>Use case not found</div>;
  }

  return (
    <UseCaseBase id={id} basePath={basePath}>
      {/* Help content specific to Exchange Rate Review */}
      <div className="mt-4 ml-11 mb-8">
        <button
          onClick={() => setShowHelp(!showHelp)}
          aria-expanded={showHelp}
          aria-controls="help-content"
          className="text-accent hover:text-accent-600 font-medium flex items-center"
        >
          Learn more {showHelp ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
        </button>

        {showHelp && (
          <div id="help-content" className="mt-1 bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-700 mb-6">
              Exchange rate validation ensures that the correct currency conversion rates are used when combining financial data from subsidiaries into your group's main reporting currency.
              Using incorrect exchange rates can lead to errors in your consolidated financial statements.
              This validation process helps maintain accuracy across all your financial reporting.
            </p>

            <div className="space-y-6">
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Key Objectives</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Compare rates in the Consolidation system against official Group reference rates</li>
                  <li>Find any rate differences</li>
                </ul>
              </div>

              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Required Information</h4>
                <p className="text-gray-700">To perform exchange rate validation, you'll need:</p>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Currency Pairs: Each subsidiary currency paired with your main consolidation currency</li>
                  <li>Rate Types: Opening rates, closing rates, and average rates</li>
                </ul>
              </div>

              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Source Documents</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Group exchange rates</li>
                  <li>Consolidation system rates</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Step Content */}
      <Routes>
        <Route index element={<DataInputStep useCase={useCase} />} />
        <Route path="/processing" element={<ProcessingStep useCase={useCase} />} />
        <Route path="/results" element={<ResultsStep useCase={useCase} />} />
        <Route path="/end" element={
          <div className="max-w-4xl mx-auto">
            <EndStep useCase={useCase} />
          </div>
        } />
      </Routes>
    </UseCaseBase>
  );
}

// Wrap component with route restore HOC
export default withUseCaseRouteRestore(ExchangeRateReview, 'exchange-rate-review');
