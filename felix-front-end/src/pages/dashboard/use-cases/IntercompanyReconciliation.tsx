import { Routes, Route } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';
import UseCaseBase from '../../../components/use-case/UseCaseBase';
import DataInputStep from '../steps/DataInputStep';
import ProcessingStep from '../steps/ProcessingStep';
import ResultsStep from '../steps/ResultsStep';
import CommunicationStep from '../steps/CommunicationStep';
import EndStep from '../steps/EndStep';
import { getUseCaseById } from '../../../data/useCases';
import { useState } from 'react';
import withUseCaseRouteRestore from '../../../components/use-case/withUseCaseRouteRestore';

/**
 * IntercompanyReconciliation component handles the workflow for the Intercompany Reconciliation use case.
 */
function IntercompanyReconciliation() {
  const id = 'intercompany-reconciliation';
  const useCase = getUseCaseById(id);
  const [showHelp, setShowHelp] = useState(false);
  const basePath = `/dashboard/use-case/${id}`;

  if (!useCase) {
    return <div>Use case not found</div>;
  }

  return (
    <UseCaseBase id={id} basePath={basePath}>
      {/* Help content specific to Intercompany Reconciliation */}
      <div className="mt-4 ml-11 mb-8">
        <button
          onClick={() => setShowHelp(!showHelp)}
          aria-expanded={showHelp}
          aria-controls="help-content"
          className="text-accent hover:text-accent-600 font-medium flex items-center"
        >
          Learn more {showHelp ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
        </button>

        {showHelp && (
          <div id="help-content" className="mt-1 bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-700 mb-6">
              This case handles a critical pre-consolidation step by identifying and resolving discrepancies
              between intercompany transactions. It focuses on matching balances between entities in local,
              consolidated, and transaction currencies to ensure elimination entries are accurate.
              Felix enables the client to specify materiality thresholds for variances, which may differ based on the
              sensitivity of accounts.
              For variances exceeding these thresholds, Felix generates automated emails to notify the involved
              subsidiaries for resolution.
            </p>

            <div className="space-y-6">
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Data Input</h4>
                <div className="space-y-2 text-gray-700">
                  <p><span className="font-medium">Source Documents:</span> Intercompany reconciliation tables including assets/liabilities balances at
                  closing per entity as well as income statement YTD positions, and dividends distribution</p>
                  <div>
                    <p className="font-medium mb-1">Required Elements:</p>
                    <ol className="list-decimal pl-5 space-y-1">
                      <li>Account Information: Account codes and descriptions (e.g., trade receivables, loans, borrowings)</li>
                      <li>Local Currency Balances (optional): Balances reported in the local currency of each entity (e.g.KEUR)</li>
                      <li>Consolidated Currency Balances: Balances converted into the consolidated currency (e.g.KCHF)</li>
                      <li>Transaction Currency (optional if available): For some clients, transaction currency used for trading between the 2 entities may also be included (e.g.USD)</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Step Content */}
      <Routes>
        <Route index element={<DataInputStep useCase={useCase} />} />
        <Route path="/processing" element={<ProcessingStep useCase={useCase} />} />
        <Route path="/results" element={<ResultsStep useCase={useCase} />} />
        <Route path="/communication" element={<CommunicationStep useCase={useCase} />} />
        <Route path="/end" element={
          <div className="max-w-4xl mx-auto">
            <EndStep useCase={useCase} />
          </div>
        } />
      </Routes>
    </UseCaseBase>
  );
}

// Wrap component with route restore HOC
export default withUseCaseRouteRestore(IntercompanyReconciliation, 'intercompany-reconciliation');
