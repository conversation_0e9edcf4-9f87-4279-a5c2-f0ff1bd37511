import { Routes, Route, useLocation } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';
import UseCaseBase from '../../../components/use-case/UseCaseBase';
import DataInputStep from '../steps/DataInputStep';
import ProcessingStep from '../steps/ProcessingStep';
import ResultsStep from '../steps/ResultsStep';
import CommunicationStep from '../steps/CommunicationStep';
import EndStep from '../steps/EndStep';
import { getUseCaseById } from '../../../data/useCases';
import { useState, useEffect } from 'react';
import withUseCaseRouteRestore from '../../../components/use-case/withUseCaseRouteRestore';

/**
 * AnalyticalReview component handles the workflow for the Analytical Review use case.
 */
function AnalyticalReview() {
  const id = 'analytical-review';
  const useCase = getUseCaseById(id);
  const [showHelp, setShowHelp] = useState(false);
  const basePath = `/dashboard/use-case/${id}`;
  const location = useLocation();

  // Store the current step path in session storage to help with page refreshes
  useEffect(() => {
    if (location.pathname.includes(basePath)) {
      sessionStorage.setItem(`${id}_current_step`, location.pathname);
    }
  }, [location.pathname, basePath, id]);

  if (!useCase) {
    return <div>Use case not found</div>;
  }

  return (
    <UseCaseBase id={id} basePath={basePath}>
      {/* Help content specific to Analytical Review */}
      <div className="mt-4 ml-11 mb-8">
        <button
          onClick={() => setShowHelp(!showHelp)}
          aria-expanded={showHelp}
          aria-controls="help-content"
          className="text-accent hover:text-accent-600 font-medium flex items-center"
        >
          Learn more {showHelp ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
        </button>

        {showHelp && (
          <div id="help-content" className="mt-1 bg-gray-50 p-6 rounded-lg">
            <p className="text-gray-700 mb-6">
              Our variance analysis AI serves as your first-line quality control for financial data, detecting discrepancies between actual results, budgets, and historical figures before consolidation. This early detection system helps identify potential errors and reveal important business trends requiring attention.
            </p>

            <div className="space-y-6">
              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Key Benefits</h4>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Catch reporting errors before they impact consolidated statements</li>
                  <li>Identify significant business trends requiring management action</li>
                  <li>Enhance overall financial reporting accuracy</li>
                </ul>
              </div>

              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Data Requirements</h4>
                <div className="space-y-2 text-gray-700">
                  <p><span className="font-medium">Source Data:</span> Financial reports from your ERP or Financial software</p>
                  <div>
                    <p className="font-medium mb-1">Required Fields:</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Reporting period</li>
                      <li>Company name and codes</li>
                      <li>Account numbers/descriptions</li>
                      <li>Analytical dimensions (cost centers, etc.)</li>
                      <li>Cumulative monthly data (actual, budget, prior year)</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-1">
                <h4 className="text-sm font-semibold text-gray-900">Format Flexibility</h4>
                <p className="text-gray-700">
                  Felix accommodates various accounting reports format. Simply upload your data in the supported format (CSV or PDF), and Felix will automatically process the variance analysis for your review.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Step Content */}
      <Routes>
        <Route index element={<DataInputStep useCase={useCase} />} />
        <Route path="/processing" element={<ProcessingStep useCase={useCase} />} />
        <Route path="/results" element={<ResultsStep useCase={useCase} />} />
        <Route path="/communication" element={<CommunicationStep useCase={useCase} />} />
        <Route path="/end" element={
          <div className="max-w-4xl mx-auto">
            <EndStep useCase={useCase} />
          </div>
        } />
      </Routes>
    </UseCaseBase>
  );
}

// Wrap component with route restore HOC
export default withUseCaseRouteRestore(AnalyticalReview, 'analytical-review');
