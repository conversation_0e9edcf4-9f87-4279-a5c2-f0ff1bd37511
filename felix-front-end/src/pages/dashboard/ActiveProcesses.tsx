import { useState } from 'react';
import { Clock, Filter, RefreshCw, Search } from 'lucide-react';
import { Card, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { useCases } from '../../data/useCases';

// Dummy data for active processes
const activeProcessesData = [
  {
    id: '1001',
    type: 'analytical-review',
    startDate: '2025-04-01T09:15:00Z',
    status: 'In Progress',
    entities: ['Global HQ', 'UK Subsidiary', 'FR Subsidiary'],
    issuesIdentified: 4,
    responsesReceived: 2
  },
  {
    id: '1002',
    type: 'exchange-rate-review',
    startDate: '2025-04-01T10:30:00Z',
    status: 'Awaiting Review',
    entities: ['All Subsidiaries'],
    issuesIdentified: 2,
    responsesReceived: 0
  },
  {
    id: '1003',
    type: 'intercompany-reconciliation',
    startDate: '2025-03-31T14:45:00Z',
    status: 'Pending Responses',
    entities: ['UK Subsidiary', 'US Subsidiary'],
    issuesIdentified: 7,
    responsesReceived: 3
  }
];

// Dummy data for communication tracking
const emailCommunicationData = {
  stats: {
    delivered: 13,
    opened: 11,
    responded: 5,
    avgResponseTime: '4.2 hours'
  },
  emails: [
    {
      id: 'e001',
      recipient: '<EMAIL>',
      entity: 'UK Subsidiary',
      sentDate: '2025-04-01T09:45:00Z',
      status: 'Delivered',
      dueDate: '2025-04-02T09:45:00Z',
      responseStatus: 'Pending'
    },
    {
      id: 'e002',
      recipient: '<EMAIL>',
      entity: 'FR Subsidiary',
      sentDate: '2025-04-01T09:45:00Z',
      status: 'Opened',
      dueDate: '2025-04-02T09:45:00Z',
      responseStatus: 'Responded'
    },
    {
      id: 'e003',
      recipient: '<EMAIL>',
      entity: 'DE Subsidiary',
      sentDate: '2025-03-31T15:00:00Z',
      status: 'Opened',
      dueDate: '2025-04-01T15:00:00Z',
      responseStatus: 'Responded'
    },
    {
      id: 'e004',
      recipient: '<EMAIL>',
      entity: 'US Subsidiary',
      sentDate: '2025-03-31T15:00:00Z',
      status: 'Delivered',
      dueDate: '2025-04-01T15:00:00Z',
      responseStatus: 'Pending'
    },
    {
      id: 'e005',
      recipient: '<EMAIL>',
      entity: 'CA Subsidiary',
      sentDate: '2025-03-31T15:00:00Z',
      status: 'Opened',
      dueDate: '2025-04-01T15:00:00Z',
      responseStatus: 'Overdue'
    }
  ]
};

// Icons mapping for types
const icons = {
  BarChart3: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="3" height="10" x="3" y="10" rx="1"/><rect width="3" height="18" x="10" y="2" rx="1"/><rect width="3" height="14" x="17" y="6" rx="1"/></svg>,
  CircleDollarSign: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"/><path d="M12 18V6"/></svg>,
  Network: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="16" y="16" width="6" height="6" rx="1"/><rect x="2" y="16" width="6" height="6" rx="1"/><rect x="9" y="2" width="6" height="6" rx="1"/><path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"/><path d="M12 12V8"/></svg>,
  Calculator: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="16" height="20" x="4" y="2" rx="2"/><line x1="8" x2="16" y1="6" y2="6"/><line x1="16" x2="16" y1="14" y2="18"/><path d="M16 10h.01"/><path d="M12 10h.01"/><path d="M8 10h.01"/><path d="M12 14h.01"/><path d="M8 14h.01"/><path d="M12 18h.01"/><path d="M8 18h.01"/></svg>
};

export default function ActiveProcesses() {
  const [activeTab, setActiveTab] = useState('processes');

  const renderIcon = (type: string) => {
    const useCase = useCases.find(uc => uc.id === type);
    if (!useCase) return null;
    
    const IconComponent = icons[useCase.icon as keyof typeof icons];
    if (!IconComponent) return null;
    
    return <IconComponent className="h-5 w-5 text-primary" />;
  };

  const formatDateTime = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'awaiting review':
        return 'bg-orange-100 text-orange-800';
      case 'pending responses':
        return 'bg-purple-100 text-purple-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmailStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-blue-100 text-blue-800';
      case 'opened':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getResponseStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'responded':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Active Processes</h1>
        <p className="text-gray-600">Track ongoing financial consolidation processes and communications</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'processes'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('processes')}
        >
          Process Tracking
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'communications'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('communications')}
        >
          Email Communication
        </button>
      </div>

      {activeTab === 'processes' && (
        <>
          {/* Process Controls */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search processes..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Filter size={16} />}
              >
                Filter
              </Button>
              <Button
                variant="outline"
                size="sm"
                leftIcon={<RefreshCw size={16} />}
              >
                Refresh
              </Button>
            </div>
          </div>

          {/* Process Table */}
          <Card variant="bordered">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entities</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issues</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responses</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {activeProcessesData.map((process) => {
                      const useCase = useCases.find(uc => uc.id === process.type);
                      
                      return (
                        <tr key={process.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {process.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="mr-2">
                                {renderIcon(process.type)}
                              </div>
                              <span className="font-medium text-gray-700">{useCase?.name || process.type}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {formatDateTime(process.startDate)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusClass(process.status)}`}>
                              {process.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.entities.length > 1 ? 
                              `${process.entities[0]} +${process.entities.length - 1} more` : 
                              process.entities[0]
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.issuesIdentified}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.responsesReceived} / {process.issuesIdentified}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <Button variant="ghost" size="sm">
                              View Details
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {activeTab === 'communications' && (
        <>
          {/* Email Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Emails Delivered</div>
                <div className="text-2xl font-bold text-primary">{emailCommunicationData.stats.delivered}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Emails Opened</div>
                <div className="text-2xl font-bold text-primary">{emailCommunicationData.stats.opened}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Responses Received</div>
                <div className="text-2xl font-bold text-primary">{emailCommunicationData.stats.responded}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Avg. Response Time</div>
                <div className="text-2xl font-bold text-primary">{emailCommunicationData.stats.avgResponseTime}</div>
              </CardContent>
            </Card>
          </div>

          {/* Email Controls */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search emails..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Filter size={16} />}
              >
                Filter
              </Button>
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Clock size={16} />}
              >
                Send Reminder
              </Button>
            </div>
          </div>

          {/* Email Table */}
          <Card variant="bordered">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entity</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {emailCommunicationData.emails.map((email) => (
                      <tr key={email.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {email.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700">
                          {email.recipient}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                          {email.entity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                          {formatDateTime(email.sentDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${getEmailStatusClass(email.status)}`}>
                            {email.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                          {formatDateTime(email.dueDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs rounded-full ${getResponseStatusClass(email.responseStatus)}`}>
                            {email.responseStatus}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}