import { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import * as Progress from '@radix-ui/react-progress';
import { Check, Brain, AlertTriangle, FileType, ClipboardCheck } from 'lucide-react';
import { Card, CardContent } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { UseCase } from '../../../data/useCases';
import { getSavedStepStatus, updateUseCaseStepStatus, getUseCaseRequestId } from '../../../utils/localStorage';
import { useUseCaseContext } from '../../../contexts/UseCaseContext';
import config from '../../../config';
import processService from '../../../api/services/processService';
import requestService from '../../../api/services/requestService';

interface ProcessingStepProps {
  useCase: UseCase;
}

interface ProcessingStage {
  name: string;
  status: 'pending' | 'active' | 'completed';
  progress: number;
}

export default function ProcessingStep({ useCase }: ProcessingStepProps) {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [overallProgress, setOverallProgress] = useState(0);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [consoleLog, setConsoleLog] = useState<string[]>([]);
  const [autoScroll, setAutoScroll] = useState(true);
  const [isResuming, setIsResuming] = useState(false);

  const [stages, setStages] = useState<ProcessingStage[]>([
    { name: 'Validation', status: 'pending', progress: 0 },
    { name: 'Analysis', status: 'pending', progress: 0 },
    { name: 'Quality Assurance', status: 'pending', progress: 0 },
    { name: 'Results Generation', status: 'pending', progress: 0 }
  ]);

  const {
    requestId,
    setRequestId,
    setLiveRequestStatus,
    setProcessingError,
  } = useUseCaseContext();

  // Check for saved processing status and request ID
  useEffect(() => {
    if (!id) return;

    const savedStatus = getSavedStepStatus(id);
    const savedRequestId = getUseCaseRequestId(id);

    console.log(`[ProcessingStep] Checking saved status for ${id}:`, savedStatus);
    console.log(`[ProcessingStep] Checking saved request ID for ${id}:`, savedRequestId);

    // If we have a saved request ID but no request ID in context, set it
    if (savedRequestId && !requestId) {
      console.log(`[ProcessingStep] Restoring saved request ID: ${savedRequestId}`);
      setRequestId(savedRequestId);
    }

    if (savedStatus === 'completed') {
      // If processing was already completed, skip to the end state
      setIsResuming(true);
      setProcessingComplete(true);
      setOverallProgress(100);
      setStages(prevStages =>
        prevStages.map(stage => ({ ...stage, status: 'completed', progress: 100 }))
      );
      addToConsole('Resuming previously completed processing session');
      addToConsole('Processing complete! Results ready for review.');
    }
  }, [id, requestId, setRequestId]);

  // Simulated processing
  useEffect(() => {
    // Skip if we're resuming a completed process
    if (isResuming) return;

    // Simulate starting the process
    addToConsole('Starting Felix AI processing for ' + useCase.name);
    addToConsole('Initializing data validation...');

    // Simulate validation stage
    const stageDelays = [0, 5000, 10000, 14000]; // Delays for each stage start
    const stageDurations = [5000, 5000, 4000, 3000]; // Duration of each stage

    // Function to update a specific stage
    const updateStage = (index: number, incrementAmount: number) => {
      setStages(prevStages =>
        prevStages.map((stage, i) =>
          i === index
            ? { ...stage, progress: Math.min(100, stage.progress + incrementAmount) }
            : stage
        )
      );
    };

    // Start each stage after appropriate delay
    stages.forEach((_, index) => {
      // Start the stage
      const startTimer = setTimeout(() => {
        // Set current stage to active and previous to completed
        setStages(prevStages =>
          prevStages.map((s, i) => {
            if (i === index) return { ...s, status: 'active' };
            if (i === index - 1) return { ...s, status: 'completed', progress: 100 };
            return s;
          })
        );

        // Add console messages for stage start
        switch(index) {
          case 0:
            addToConsole('Beginning data validation...');
            break;
          case 1:
            addToConsole('Validation complete. No critical errors found.');
            addToConsole('Starting AI analysis...');

            // Use case specific messages
            if (useCase.id === 'analytical-review') {
              addToConsole('Processing variance analysis across financial statements');
            } else if (useCase.id === 'exchange-rate-review') {
              addToConsole('Analyzing exchange rate consistency across system and treasury rates');
            } else if (useCase.id === 'intercompany-reconciliation') {
              addToConsole('Examining intercompany balance discrepancies');
            } else if (useCase.id === 'rounding-issue-resolution') {
              addToConsole('Identifying potential rounding inconsistencies in financial statements');
            }
            break;
          case 2:
            addToConsole('Analysis complete. Performing quality assurance checks...');

            // Use case specific messages
            if (useCase.id === 'analytical-review') {
              addToConsole('Found 12 significant variances requiring review');
              addToConsole('Checking variance explanations against historical patterns');
            } else if (useCase.id === 'exchange-rate-review') {
              addToConsole('Detected 3 inconsistent exchange rates');
              addToConsole('Verifying rate source accuracy');
            } else if (useCase.id === 'intercompany-reconciliation') {
              addToConsole('Identified 8 reconciliation issues across 3 entity pairs');
              addToConsole('Validating materiality impact');
            } else if (useCase.id === 'rounding-issue-resolution') {
              addToConsole('Detected 5 potential rounding errors');
              addToConsole('Verifying cross-reference integrity');
            }
            break;
          case 3:
            addToConsole('Quality assurance complete. Generating final results...');
            break;
        }

        // Progress updates for the stage
        const updateInterval = stageDurations[index] / 20; // 20 updates per stage
        const incrementAmount = 5; // 5% per update

        // Create interval to update progress
        let count = 0;
        const progressInterval = setInterval(() => {
          count++;
          updateStage(index, incrementAmount);

          // Add some simulated console outputs during processing
          if (index === 1 && count === 6) {
            if (useCase.id === 'analytical-review') {
              addToConsole('Comparing budget vs actuals for Q1 2025');
            } else if (useCase.id === 'exchange-rate-review') {
              addToConsole('Analyzing EUR/CHF rate fluctuations');
            }
          }

          if (index === 1 && count === 12) {
            if (useCase.id === 'intercompany-reconciliation') {
              addToConsole('Processing USD transactions between UK and US subsidiaries');
            } else if (useCase.id === 'rounding-issue-resolution') {
              addToConsole('Analyzing balance sheet totals for potential rounding errors');
            }
          }

          // Update overall progress based on all stages
          const overallProgressValue = stages.reduce(
            (sum, s) => sum + (s.progress / 100) * (100 / stages.length),
            0
          );
          setOverallProgress(overallProgressValue);

          // If this is the last update of the last stage, mark processing as complete
          if (index === stages.length - 1 && count >= 20) {
            clearInterval(progressInterval);
            setStages(prevStages =>
              prevStages.map((s, i) =>
                i === index ? { ...s, status: 'completed', progress: 100 } : s
              )
            );
            addToConsole('Processing complete! Results ready for review.');
            setProcessingComplete(true);

            // Update the use case step status to completed
            if (id) {
              updateUseCaseStepStatus(id, 'completed');
              console.log(`[ProcessingStep] Updated status for ${id} to completed`);
            }
          }
        }, updateInterval);

        // Clean up interval when the component unmounts
        return () => clearInterval(progressInterval);
      }, stageDelays[index]);

      // Clean up the timeout when component unmounts
      return () => clearTimeout(startTimer);
    });
  }, []);

  useEffect(() => {
    // Clear any previous processing errors when entering this step
    setProcessingError(null);

    // Reference to the cleanup function for the status stream
    let cleanupStatusStream: (() => void) | null = null;

    if (requestId) {
      setLiveRequestStatus('Initiating processing request...');

      // First, call the process API using our service
      const initiateProcessing = async () => {
        try {
          console.log('[ProcessingStep] Initiating processing request...', requestId);
          await processService.processRequest(requestId);
          console.log('[ProcessingStep] Processing initiated successfully');
          setLiveRequestStatus('Processing initiated. Connecting to status stream...');
          return true;
        } catch (error) {
          console.error('[ProcessingStep] Error initiating processing:', error);
          setProcessingError(error instanceof Error ? error.message : 'Failed to initiate processing. Please try again.');
          return false;
        }
      };

      // Call the process API and then set up the EventSource
      initiateProcessing().then(success => {
        if (!success) return;

        // Connect to the status stream using the request service
        cleanupStatusStream = requestService.connectToStatusStream(requestId, {
          onOpen: () => {
            setLiveRequestStatus('Connection established. Waiting for updates...');
          },
          onMessage: (message) => {
            setLiveRequestStatus(message);
            console.log(`[ProcessingStep] Updated live status to: ${message}`);
          },
          onError: (error) => {
            setLiveRequestStatus('Connection error with status stream.');
            setProcessingError('Failed to connect to the status stream. The process might be running in the background.');
            console.error('[ProcessingStep] Error connecting to status stream:', error);
          },
          onCompleted: () => {
            // Optional: Handle completion specifically in the UI if needed
            console.log('[ProcessingStep] Process completed.');
          }
        });
      });

      // Cleanup function
      return () => {
        console.log('[ProcessingStep] Cleaning up resources.');
        if (cleanupStatusStream) {
          cleanupStatusStream();
        }
      };
    } else {
      setLiveRequestStatus('Request ID not available. Cannot fetch status.');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestId, setLiveRequestStatus, setProcessingError]);

  // Function to add message to console log
  const addToConsole = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setConsoleLog(prevLog => [...prevLog, `[${timestamp}] ${message}`]);
  };

  // Auto-scroll console log
  useEffect(() => {
    if (autoScroll) {
      const consoleElement = document.getElementById('console-log');
      if (consoleElement) {
        consoleElement.scrollTop = consoleElement.scrollHeight;
      }
    }
  }, [consoleLog, autoScroll]);

  // View Results function
  const viewResults = () => {
    if (id) {
      // Make sure the status is set to completed before navigating
      updateUseCaseStepStatus(id, 'completed');
    }
    navigate(`/dashboard/use-case/${id}/results`);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Processing Visualization */}
      <Card className="lg:col-span-1">
        <CardContent className="p-6">
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <div className="text-sm font-medium text-gray-700">Overall Progress</div>
              <div className="text-sm font-medium text-gray-700">{Math.round(overallProgress)}%</div>
            </div>
            <Progress.Root
              className="relative overflow-hidden bg-gray-200 rounded-full w-full h-3"
              value={overallProgress}
            >
              <Progress.Indicator
                className="bg-accent w-full h-full transition-transform duration-300"
                style={{ transform: `translateX(-${100 - overallProgress}%)` }}
              />
            </Progress.Root>
          </div>

          <div className="space-y-8 relative">
            {/* Vertical line connecting stages */}
            <div className="absolute left-6 top-6 bottom-6 w-0.5 bg-gray-200" />

            {stages.map((stage, index) => (
              <div key={stage.name} className="flex items-start relative">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center z-10
                  ${stage.status === 'pending' ? 'bg-gray-200' : ''}
                  ${stage.status === 'active' ? 'bg-blue-100 text-blue-700' : ''}
                  ${stage.status === 'completed' ? 'bg-accent text-white' : ''}
                `}>
                  {stage.status === 'completed' ? (
                    <Check className="h-6 w-6" />
                  ) : index === 0 ? (
                    <FileType className="h-6 w-6" />
                  ) : index === 1 ? (
                    <Brain className="h-6 w-6" />
                  ) : index === 2 ? (
                    <AlertTriangle className="h-6 w-6" />
                  ) : (
                    <ClipboardCheck className="h-6 w-6" />
                  )}
                </div>

                <div className="ml-4 flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="text-lg font-medium text-gray-800">{stage.name}</h4>
                    <div className="text-sm font-medium">
                      {stage.status === 'completed' ? (
                        <span className="text-accent">Completed</span>
                      ) : stage.status === 'active' ? (
                        <span className="text-blue-600">{stage.progress}%</span>
                      ) : (
                        <span className="text-gray-400">Pending</span>
                      )}
                    </div>
                  </div>

                  {/* Stage progress bar */}
                  {stage.status !== 'pending' && (
                    <Progress.Root
                      className="relative overflow-hidden bg-gray-200 rounded-full w-full h-2"
                      value={stage.progress}
                    >
                      <Progress.Indicator
                        className={`w-full h-full transition-transform duration-300 ${
                          stage.status === 'completed' ? 'bg-accent' : 'bg-blue-500'
                        }`}
                        style={{ transform: `translateX(-${100 - stage.progress}%)` }}
                      />
                    </Progress.Root>
                  )}
                </div>
              </div>
            ))}
          </div>

          {processingComplete && (
            <div className="mt-8 flex justify-center">
              <Button onClick={viewResults}>
                View Detailed Results
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Live Processing Feedback */}
      <Card className="lg:col-span-1">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Processing Log</h3>
            <div className="flex items-center">
              <label className="flex items-center text-sm text-gray-600">
                <input
                  type="checkbox"
                  checked={autoScroll}
                  onChange={(e) => setAutoScroll(e.target.checked)}
                  className="mr-2"
                />
                Auto-scroll
              </label>
            </div>
          </div>

          <div
            id="console-log"
            className="bg-gray-800 text-gray-100 rounded-md p-4 font-mono text-sm h-96 overflow-y-auto"
          >
            {consoleLog.map((log, index) => (
              <div key={index} className="pb-1">
                {log}
              </div>
            ))}
            {/* When processing, show a blinking cursor */}
            {!processingComplete && (
              <span className="inline-block w-2 h-4 bg-gray-100 animate-pulse"></span>
            )}
          </div>

          {processingComplete ? (
            <div className="mt-4 p-3 bg-green-50 rounded-md">
              <div className="flex items-start">
                <Check className="h-5 w-5 text-green-500 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-800">Processing completed successfully</p>
                  <p className="mt-1 text-sm text-green-700">All data has been analyzed and results are ready for review.</p>
                </div>
              </div>
            </div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="mt-4 text-red-600 border-red-200 hover:bg-red-50"
            >
              Cancel Process
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}