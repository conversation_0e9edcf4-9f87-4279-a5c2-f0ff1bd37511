import { useState, useMemo } from 'react';
import { Download, CheckCircle, ArrowLeft, FileSpreadsheet } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { UseCase } from '../../../data/useCases';
import { useNavigate } from 'react-router-dom';

interface EndStepProps {
  useCase: UseCase;
}

export default function EndStep({ useCase }: EndStepProps) {
  const navigate = useNavigate();
  const [downloadError, setDownloadError] = useState<string | null>(null);

  const handleDownload = async () => {
    try {
      setDownloadError(null);
      // Download report logic here
      const response = await fetch(`/api/reports/${processId}`);
      if (!response.ok) throw new Error('Failed to download report');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${useCase.name}-Report-${processId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      setDownloadError('Failed to download report. Please try again.');
      console.error('Download error:', error);
    }
  };
  const processId = useMemo(() => {
    const array = new Uint32Array(2);
    crypto.getRandomValues(array);
    return `${useCase.id.toUpperCase()}-${array[0].toString(36)}${array[1].toString(36)}`.slice(0, 20);
  }, [useCase.id]);

  const completionTime = new Date().toLocaleString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader className="text-center border-b border-gray-100">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-2xl">Process Complete</CardTitle>
          <p className="text-gray-600 mt-2">All tasks have been successfully completed</p>
        </CardHeader>
        
        <CardContent className="p-6">
          {/* Process Details */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <div className="text-sm text-gray-500">Process Reference</div>
                <div className="text-lg font-medium">{processId}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Completion Time</div>
                <div className="text-lg font-medium">{completionTime}</div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="font-medium mb-2">Process Summary</h3>
              <ul className="space-y-2">
                {useCase.endProcess.summary.map((item, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <h3 className="font-medium mb-2">Data Cleanup Verification</h3>
              <ul className="space-y-2">
                {useCase.endProcess.verifications.map((item, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" />
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium mb-2">Performance Metrics</h3>
              <ul className="space-y-2">
                {useCase.endProcess.report.metrics.map((metric, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                    {metric}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-100">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard')}
              leftIcon={<ArrowLeft size={16} />}
            >
              Return to Dashboard
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                leftIcon={<FileSpreadsheet size={16} />}
              >
                Export to Excel
              </Button>
              <Button
                onClick={handleDownload}
                leftIcon={<Download size={16} />}
              >
                Download Report
              </Button>
            </div>
          </div>
          {downloadError && (
            <div className="mt-4 text-red-600 text-sm text-center">
              {downloadError}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}