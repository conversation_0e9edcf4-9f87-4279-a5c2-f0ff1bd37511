import { useState, useRef, useMemo, useEffect } from 'react';
import { AlertTriangle, Download, Filter, Search, ArrowDown, ArrowUp, SortAsc, SortDesc, FileSpreadsheet, X, Loader } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { UseCase } from '../../../data/useCases';
import { useUseCaseContext } from '../../../contexts/UseCaseContext';
import processService from '../../../api/services/processService';
import requestService from '../../../api/services/requestService';
import { getUseCaseRequestId } from '../../../utils/localStorage';
import {
  SpreadsheetComponent,
  SheetsDirective,
  SheetDirective,
  RangesDirective,
  RangeDirective,
  ColumnsDirective,
  ColumnDirective,
  getCellAddress,
} from '@syncfusion/ej2-react-spreadsheet';
import {
  Dialog,
  DialogTrigger,
  DialogPortal,
  DialogOverlay,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from '@radix-ui/react-dialog';

interface ResultsStepProps {
  useCase: UseCase;
}

interface SpreadsheetPopupProps {
  isOpen: boolean;
  onClose: () => void;
  data: FinancialSpreadsheetRow[];
  columnDefinitions: { header: string, field: keyof FinancialSpreadsheetRow, width?: number }[];
}

// Define a type for the new spreadsheet data
interface FinancialSpreadsheetRow {
  id: string;
  subsidiary: string;
  glAccountNumber: string;
  glAccountName: string;
  costCenter: string;
  period: string;
  currency: string;
  actualAmount: number;
  budgetAmount: number;
  varianceAmount: number;
  notes?: string;
}

// Generate dummy data based on use case type
const generateDummyData = (useCaseId: string) => {
  switch(useCaseId) {
    case 'analytical-review':
      return Array(12).fill(0).map((_, i) => ({
        id: `AR${i + 1}`,
        subsidiaryCode: `SUB${i % 5 + 1}`,
        subsidiaryName: [`Global HQ`, `UK Subsidiary`, `US Subsidiary`, `FR Subsidiary`, `DE Subsidiary`][i % 5],
        account: [`Revenue`, `Cost of Sales`, `Operating Expenses`, `Administrative Expenses`, `Financial Income`, `Income Tax`][i % 6],
        accountNumber: `${(i % 6) + 1}000`,
        actual: (Math.random() * 1000000 + 500000).toFixed(2),
        budget: (Math.random() * 1000000 + 500000).toFixed(2),
        variance: (Math.random() * 200000 - 100000).toFixed(2),
        variancePercent: `${(Math.random() * 40 - 20).toFixed(2)}%`,
        significance: Math.random() > 0.5 ? 'High' : 'Medium',
        recheck: 'No'
      }));

    case 'exchange-rate-review':
      const currencies = ['USD/CHF', 'EUR/CHF', 'GBP/CHF', 'JPY/CHF', 'CNY/CHF'];
      return Array(8).fill(0).map((_, i) => ({
        id: `ER${i + 1}`,
        currency: currencies[i % currencies.length],
        rateType: ['Opening', 'Closing', 'Average'][i % 3],
        sourceRate: (Math.random() * 0.2 + 0.9).toFixed(4),
        systemRate: (Math.random() * 0.2 + 0.9).toFixed(4),
        variance: (Math.random() * 0.02 - 0.01).toFixed(4),
        status: Math.random() > 0.7 ? 'Error' : Math.random() > 0.4 ? 'Warning' : 'Valid'
      }));

    case 'intercompany-reconciliation':
      const entityPairs = [
        {from: 'UK Subsidiary', to: 'US Subsidiary'},
        {from: 'Global HQ', to: 'DE Subsidiary'},
        {from: 'FR Subsidiary', to: 'UK Subsidiary'},
        {from: 'US Subsidiary', to: 'Global HQ'}
      ];
      const accounts = [
        { number: '1200', name: 'Accounts Receivable' },
        { number: '2100', name: 'Accounts Payable' },
        { number: '1400', name: 'Loans' },
        { number: '4200', name: 'Interest' }
      ];
      return Array(16).fill(0).map((_, i) => ({
        id: `IC${i + 1}`,
        fromEntity: entityPairs[i % entityPairs.length].from,
        toEntity: entityPairs[i % entityPairs.length].to,
        accountNumber: accounts[i % accounts.length].number,
        accountName: accounts[i % accounts.length].name,
        fromAmount: (Math.random() * 500000 + 100000).toFixed(2),
        toAmount: (Math.random() * 500000 + 100000).toFixed(2),
        currency: ['USD', 'EUR', 'GBP', 'CHF'][i % 4],
        variance: (Math.random() * 20000 - 10000).toFixed(2),
        variancePercent: `${(Math.random() * 8 - 4).toFixed(2)}%`
      }));

    case 'rounding-issue-resolution':
      return Array(8).fill(0).map((_, i) => ({
        id: `RI${i + 1}`,
        financialTable: [`Balance Sheet`, `Income Statement`, `Cash Flow`, `Statement of Changes in Equity`][i % 4],
        tableReference: `TABLE_${i + 1}`,
        issuesDetected: Math.floor(Math.random() * 5) + 1
      }));

    default:
      return [];
  }
};

// New function to generate detailed financial data for the spreadsheet
const generateSpreadsheetFinancialData = (rowCount: number = 75): FinancialSpreadsheetRow[] => {
  const subsidiaries = ['Global HQ', 'UK Subsidiary', 'US Subsidiary', 'FR Subsidiary', 'DE Subsidiary', 'JP Subsidiary'];
  const glAccounts = [
    { number: '400000', name: 'Revenue - Product A' },
    { number: '400100', name: 'Revenue - Product B' },
    { number: '500000', name: 'Cost of Goods Sold' },
    { number: '600000', name: 'Salaries & Wages' },
    { number: '610000', name: 'Rent Expense' },
    { number: '620000', name: 'Utilities Expense' },
    { number: '630000', name: 'Marketing Expense' },
    { number: '700000', name: 'Interest Income' },
    { number: '710000', name: 'Interest Expense' },
  ];
  const costCenters = ['CC001', 'CC002', 'CC003', 'CC004', 'CC005'];
  const periods = Array.from({ length: 12 }, (_, i) => {
    const month = (i + 1).toString().padStart(2, '0');
    return `2024-${month}-01`;
  });
  const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CHF'];
  const data: FinancialSpreadsheetRow[] = [];

  for (let i = 0; i < rowCount; i++) {
    const actual = Math.random() * 500000 + 10000;
    const budget = actual * (Math.random() * 0.4 + 0.8); // Budget is 80%-120% of actual
    data.push({
      id: `FSR${i + 1}`,
      subsidiary: subsidiaries[i % subsidiaries.length],
      glAccountNumber: glAccounts[i % glAccounts.length].number,
      glAccountName: glAccounts[i % glAccounts.length].name,
      costCenter: costCenters[i % costCenters.length],
      period: periods[i % periods.length],
      currency: currencies[i % currencies.length],
      actualAmount: parseFloat(actual.toFixed(2)),
      budgetAmount: parseFloat(budget.toFixed(2)),
      varianceAmount: parseFloat((actual - budget).toFixed(2)),
      notes: Math.random() > 0.8 ? 'Needs review' : ''
    });
  }
  return data;
};

const getSpreadsheetColumnDefinitions = (): { header: string, field: keyof FinancialSpreadsheetRow, width?: number }[] => {
  return [
    { header: 'Subsidiary', field: 'subsidiary', width: 150 },
    { header: 'GL Account #', field: 'glAccountNumber', width: 120 },
    { header: 'GL Account Name', field: 'glAccountName', width: 180 },
    { header: 'Cost Center', field: 'costCenter', width: 100 },
    { header: 'Period', field: 'period', width: 100 },
    { header: 'Currency', field: 'currency', width: 80 },
    { header: 'Actual Amount', field: 'actualAmount', width: 130 },
    { header: 'Budget Amount', field: 'budgetAmount', width: 130 },
    { header: 'Variance Amount', field: 'varianceAmount', width: 130 },
    { header: 'Notes', field: 'notes', width: 200 },
  ];
};

const SpreadsheetPopup: React.FC<SpreadsheetPopupProps> = ({ isOpen, onClose, data, columnDefinitions }) => {
  const spreadsheetRef = useRef<SpreadsheetComponent>(null);

  const sheetDataSource = useMemo(() => {
    return data.map(item => {
      const transformedItem: { [key: string]: any } = {};
      columnDefinitions.forEach(colDef => {
        transformedItem[colDef.header] = item[colDef.field];
      });
      return transformedItem;
    });
  }, [data, columnDefinitions]);

  const handleDataBound = () => {
    const spreadsheet = spreadsheetRef.current;
    if (spreadsheet && sheetDataSource.length > 0) {
      const headerRange = `A1:${getCellAddress(0, columnDefinitions.length - 1)}`;
      spreadsheet.cellFormat({ fontWeight: 'bold', textAlign: 'center' }, headerRange);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open: boolean) => !open && onClose()}>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 data-[state=open]:animate-overlayShow" />
        <DialogContent
          className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[95vw] h-[90vh] max-w-[1800px] max-h-[1000px] bg-white shadow-xl flex flex-col p-0 sm:rounded-lg data-[state=open]:animate-contentShow"
        >
          <div className="flex items-center justify-between p-4 border-b shrink-0">
            <DialogTitle className="text-lg font-semibold">Edit in Spreadsheet</DialogTitle>
            <DialogDescription className="sr-only">
              Spreadsheet editor for modifying table data. Use the spreadsheet interface to make changes.
            </DialogDescription>
            <DialogClose asChild>
              <Button variant="ghost" size="sm">
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </DialogClose>
          </div>
          <div className="flex-grow overflow-hidden p-4">
            <SpreadsheetComponent
              ref={spreadsheetRef}
              height="100%"
              width="100%"
              dataBound={handleDataBound}
              showRibbon={true}
            >
              <SheetsDirective>
                <SheetDirective>
                  <RangesDirective>
                    <RangeDirective dataSource={sheetDataSource}></RangeDirective>
                  </RangesDirective>
                  <ColumnsDirective>
                    {columnDefinitions.map((col, index) => (
                      <ColumnDirective key={index} width={col.width || 120}></ColumnDirective>
                    ))}
                  </ColumnsDirective>
                </SheetDirective>
              </SheetsDirective>
            </SpreadsheetComponent>
          </div>
          <div className="flex justify-end p-4 border-t shrink-0">
            <DialogClose asChild>
              <Button onClick={onClose} variant="outline">Close</Button>
            </DialogClose>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

export default function ResultsStep({ useCase }: ResultsStepProps) {
  const { requestId, setRequestId } = useUseCaseContext();
  const [resultsData, setResultsData] = useState(() => generateDummyData(useCase.id));
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiResults, setApiResults] = useState<any>(null);
  const [editedData, setEditedData] = useState<{ [key: string]: string }>({});
  const [sortColumn, setSortColumn] = useState('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSpreadsheetPopup, setShowSpreadsheetPopup] = useState(false);

  // Restore request ID from localStorage if missing
  useEffect(() => {
    if (!requestId && useCase.id) {
      const savedRequestId = getUseCaseRequestId(useCase.id);
      if (savedRequestId) {
        console.log(`[ResultsStep] Restoring saved request ID: ${savedRequestId}`);
        setRequestId(savedRequestId);
      }
    }
  }, [requestId, setRequestId, useCase.id]);

  // Fetch results when component loads
  useEffect(() => {
    const fetchResults = async () => {
      if (!requestId) {
        console.log('[ResultsStep] No request ID available, using dummy data');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log(`[ResultsStep] Fetching results for request ID: ${requestId}`);

        // First, get the request status to ensure it's completed
        const requestData = await requestService.getRequest(requestId);
        console.log('[ResultsStep] Request data:', requestData);

        // Try to get results from the request service
        const results = await requestService.getResults(requestId);
        console.log('[ResultsStep] API Results:', results);

        setApiResults(results);

        // TODO: Transform API results to match the expected format for the UI
        // For now, we'll keep using dummy data but log the real results

      } catch (error: any) {
        console.error('[ResultsStep] Error fetching results:', error);
        setError(error.message || 'Failed to fetch results');

        // If the results endpoint doesn't exist yet (404), that's expected
        if (error.message?.includes('404') || error.message?.includes('Results not found')) {
          console.log('[ResultsStep] Results endpoint not implemented yet, using dummy data');
          setError(null); // Clear error since this is expected
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, [requestId]);

  // Memoize spreadsheet data and columns to avoid regeneration on every render
  const spreadsheetFinancialData = useMemo(() => {
    if (showSpreadsheetPopup) { // Only generate when popup is to be shown
      return generateSpreadsheetFinancialData();
    }
    return []; // Return empty array if not shown, or cached data if you implement caching
  }, [showSpreadsheetPopup]);

  const spreadsheetColumnDefs = useMemo(() => getSpreadsheetColumnDefinitions(), []);

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Filter data based on search query
  const filteredData = resultsData.filter(item => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();

    // Check all properties of the item
    return Object.values(item).some(value =>
      value.toString().toLowerCase().includes(query)
    );
  });

  // Sort data based on sort column and direction
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortColumn) return 0;

    const valueA = a[sortColumn as keyof typeof a];
    const valueB = b[sortColumn as keyof typeof b];

    // Handle string comparisons
    if (typeof valueA === 'string' && typeof valueB === 'string') {
      // Try to parse as numbers first if they contain digit characters
      if (/\d/.test(valueA) && /\d/.test(valueB)) {
        // Extract numeric part if it exists
        const numA = parseFloat(valueA.replace(/[^-0-9.]/g, ''));
        const numB = parseFloat(valueB.replace(/[^-0-9.]/g, ''));

        if (!isNaN(numA) && !isNaN(numB)) {
          return sortDirection === 'asc' ? numA - numB : numB - numA;
        }
      }

      // Regular string comparison
      return sortDirection === 'asc'
        ? valueA.localeCompare(valueB)
        : valueB.localeCompare(valueA);
    }

    return 0;
  });

  // Handle recheck value change
  const handleEmailChange = (id: string, value: string) => {
    setEditedData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Render appropriate metrics based on use case
  const renderMetrics = () => {
    switch(useCase.id) {
      case 'analytical-review':
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Total Variances</div>
                <div className="text-2xl font-bold text-primary">12</div>
                <div className="text-sm text-gray-500">Across 4 entities</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">High Significance</div>
                <div className="text-2xl font-bold text-red-600">5</div>
                <div className="text-sm text-gray-500">Require attention</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Medium Significance</div>
                <div className="text-2xl font-bold text-amber-500">7</div>
                <div className="text-sm text-gray-500">For review</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Largest Variance</div>
                <div className="text-2xl font-bold text-primary">15.3%</div>
                <div className="text-sm text-gray-500">Revenue - UK Subsidiary</div>
              </CardContent>
            </Card>
          </div>
        );

      case 'exchange-rate-review':
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Rate Discrepancies</div>
                <div className="text-2xl font-bold text-primary">8</div>
                <div className="text-sm text-gray-500">Across 5 currency pairs</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Total Impact</div>
                <div className="text-2xl font-bold text-primary">213,450 CHF</div>
                <div className="text-sm text-gray-500">Potential adjustment</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Largest Rate Variance</div>
                <div className="text-2xl font-bold text-amber-500">0.92%</div>
                <div className="text-sm text-gray-500">EUR/CHF on April 2</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Date Range Analyzed</div>
                <div className="text-2xl font-bold text-primary">Q1 2025</div>
                <div className="text-sm text-gray-500">Jan 1 - Mar 31</div>
              </CardContent>
            </Card>
          </div>
        );

      case 'intercompany-reconciliation':
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Intercompany Variances</div>
                <div className="text-2xl font-bold text-primary">16</div>
                <div className="text-sm text-gray-500">Across 4 entity pairs</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Total Variance</div>
                <div className="text-2xl font-bold text-red-600">127,344 USD</div>
                <div className="text-sm text-gray-500">Net discrepancy</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Most Affected Account</div>
                <div className="text-2xl font-bold text-amber-500">Accounts Receivable</div>
                <div className="text-sm text-gray-500">7 variances</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Largest Entity Variance</div>
                <div className="text-2xl font-bold text-primary">UK-US</div>
                <div className="text-sm text-gray-500">5 outstanding items</div>
              </CardContent>
            </Card>
          </div>
        );

      case 'rounding-issue-resolution':
        return (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Rounding Corrections</div>
                <div className="text-2xl font-bold text-primary">8</div>
                <div className="text-sm text-gray-500">Across 4 statements</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">High Impact</div>
                <div className="text-2xl font-bold text-red-600">2</div>
                <div className="text-sm text-gray-500">Require review</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Cross-Reference Integrity</div>
                <div className="text-2xl font-bold text-green-600">100%</div>
                <div className="text-sm text-gray-500">Maintained</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Most Affected Statement</div>
                <div className="text-2xl font-bold text-primary">Balance Sheet</div>
                <div className="text-sm text-gray-500">3 corrections</div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  // Render table headers based on use case
  const renderTableHeaders = () => {
    switch(useCase.id) {
      case 'analytical-review':
        return (
          <tr className="bg-gray-50 text-left">
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('subsidiaryName')}>
                Subsidiary
                {sortColumn === 'subsidiaryName' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('account')}>
                Account
                {sortColumn === 'account' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('accountNumber')}>
                Account #
                {sortColumn === 'accountNumber' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('actual')}>
                Actual
                {sortColumn === 'actual' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('budget')}>
                Budget
                {sortColumn === 'budget' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('variance')}>
                Variance
                {sortColumn === 'variance' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('variancePercent')}>
                Variance %
                {sortColumn === 'variancePercent' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('significance')}>
                Significance
                {sortColumn === 'significance' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center">
                Send Email
              </div>
            </th>
          </tr>
        );

      case 'exchange-rate-review':
        return (
          <tr className="bg-gray-50 text-left">
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('currency')}>
                Currency
                {sortColumn === 'currency' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('rateType')}>
                Rate Type
                {sortColumn === 'rateType' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('sourceRate')}>
                Source Rate
                {sortColumn === 'sourceRate' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('systemRate')}>
                System Rate
                {sortColumn === 'systemRate' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('variance')}>
                Variance
                {sortColumn === 'variance' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              <div className="flex items-center justify-end cursor-pointer" onClick={() => handleSort('status')}>
                Status
                {sortColumn === 'status' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
          </tr>
        );

      case 'intercompany-reconciliation':
        return (
          <tr className="bg-gray-50 text-left">
            {[
              { label: 'From Entity', field: 'fromEntity' },
              { label: 'To Entity', field: 'toEntity' },
              { label: 'Account Number', field: 'accountNumber' },
              { label: 'Account Name', field: 'accountName' },
              { label: 'From Amount', field: 'fromAmount', align: 'right' },
              { label: 'To Amount', field: 'toAmount', align: 'right' },
              { label: 'Currency', field: 'currency' },
              { label: 'Variance', field: 'variance', align: 'right' },
              { label: 'Variance %', field: 'variancePercent', align: 'right' }
            ].map(({ label, field, align }) => (
              <th key={field} className={`px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${align === 'right' ? 'text-right' : 'text-left'}`}>
                <div className={`flex items-center ${align === 'right' ? 'justify-end' : ''} cursor-pointer`} onClick={() => handleSort(field)}>
                  {label}
                  {sortColumn === field && (
                    sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
            ))}
          </tr>
        );

      case 'rounding-issue-resolution':
        return (
          <tr className="bg-gray-50 text-left">
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('financialTable')}>
                Financial Table Name
                {sortColumn === 'table' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-left">
              <div className="flex items-center cursor-pointer" onClick={() => handleSort('tableReference')}>
                Table Reference
                {sortColumn === 'tableReference' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-center">
              <div className="flex items-center justify-center cursor-pointer" onClick={() => handleSort('issuesDetected')}>
                # of Rounding Issues Detected
                {sortColumn === 'issuesDetected' && (
                  sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                )}
              </div>
            </th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider text-right">
              Actions
            </th>
          </tr>
        );


      default:
        return null;
    }
  };

  // Render table rows based on use case
  const renderTableRows = () => {
    switch(useCase.id) {
      case 'analytical-review':
        return sortedData.map((item: any) => (
          <tr key={item.id} className="hover:bg-gray-50 border-b border-gray-100">
            <td className="px-4 py-3 text-sm text-gray-500">{item.subsidiaryName}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.account}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.accountNumber}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.actual}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.budget}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.variance}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.variancePercent}</td>
            <td className="px-4 py-3 text-sm text-gray-500">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                item.significance === 'High' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'
              }`}>
                {item.significance}
              </span>
            </td>
            <td className="px-4 py-3 text-sm text-gray-500">
              <input
                type="checkbox"
                checked={editedData[item.id] === 'Yes'}
                onChange={(e) => handleEmailChange(item.id, e.target.checked ? 'Yes' : 'No')}
                className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
              />
            </td>
          </tr>
        ));

      case 'exchange-rate-review':
        return sortedData.map((item: any) => (
          <tr key={item.id} className="hover:bg-gray-50 border-b border-gray-100">
            <td className="px-4 py-3 text-sm text-gray-500">{item.currency}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.rateType}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.sourceRate}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.systemRate}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.variance}</td>
            <td className="px-4 py-3 text-sm text-gray-500">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                item.status === 'Error' ? 'bg-red-100 text-red-800' :
                item.status === 'Warning' ? 'bg-amber-100 text-amber-800' : 'bg-green-100 text-green-800'
              }`}>
                {item.status}
              </span>
            </td>
          </tr>
        ));

      case 'intercompany-reconciliation':
        return sortedData.map((item: any) => (
          <tr key={item.id} className="hover:bg-gray-50 border-b border-gray-100">
            <td className="px-4 py-3 text-sm text-gray-500">{item.fromEntity}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.toEntity}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.accountNumber}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.accountName}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.fromAmount}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.toAmount}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.currency}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.variance}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-right">{item.variancePercent}</td>
          </tr>
        ));

      case 'rounding-issue-resolution':
        return sortedData.map((item: any) => (
          <tr key={item.id} className="hover:bg-gray-50 border-b border-gray-100">
            <td className="px-4 py-3 text-sm text-gray-500">{item.financialTable}</td>
            <td className="px-4 py-3 text-sm text-gray-500">{item.tableReference}</td>
            <td className="px-4 py-3 text-sm text-gray-500 text-center">{item.issuesDetected}</td>
            <td className="px-4 py-3 text-sm text-right">
              <Button variant="ghost" size="sm" className="text-accent" onClick={() => setShowSpreadsheetPopup(true)}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Edit in Excel
              </Button>
            </td>
          </tr>
        ));

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="p-8 text-center">
            <Loader className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-gray-600">Loading results...</p>
            {requestId && (
              <p className="text-sm text-gray-500 mt-2">Request ID: {requestId}</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <div>
                <p className="font-medium">Error loading results</p>
                <p className="text-sm text-red-500">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Debug Info */}
      {apiResults && (
        <Card>
          <CardHeader>
            <CardTitle>Debug: API Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-40">
              {JSON.stringify(apiResults, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Metrics */}
      {!isLoading && renderMetrics()}

      {/* Results Table */}
      {!isLoading && (
        <Card>
        <CardHeader>
          <CardTitle>Results</CardTitle>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <input
                type="text"
                placeholder="Search results..."
                className="pl-8 pr-4 py-2 w-full border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="sm" className="min-w-[80px]">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="min-w-[80px]">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="min-w-[100px]" onClick={() => setShowSpreadsheetPopup(true)}>
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Edit in Excel
            </Button>
            <Button size="sm" className="min-w-[80px]">
              Save
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                {renderTableHeaders()}
              </thead>
              <tbody>
                {renderTableRows()}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
      )}

      {showSpreadsheetPopup && spreadsheetFinancialData.length > 0 && (
        <SpreadsheetPopup
          isOpen={showSpreadsheetPopup}
          onClose={() => setShowSpreadsheetPopup(false)}
          data={spreadsheetFinancialData}
          columnDefinitions={spreadsheetColumnDefs}
        />
      )}
    </div>
  );
}