import { useState, useEffect } from 'react';
import { Upload, AlertCircle, CheckCircle, X } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { UseCase } from '../../../data/useCases';
import { v4 as uuidv4 } from 'uuid';
import { useUseCaseContext } from '../../../contexts/UseCaseContext';

interface DataInputStepProps {
  useCase: UseCase;
}

export default function DataInputStep({ useCase }: DataInputStepProps) {
  const [isDragging, setIsDragging] = useState(false);
  const {
    requestId,
    setRequestId,
    files,
    addFiles,
    removeFile,
    config,
    updateConfig,
    setCurrentUseCase
  } = useUseCaseContext();

  // Initialize current use case when component mounts
  useEffect(() => {
    // Set the current use case in context
    setCurrentUseCase(useCase);

    // If no request ID exists, create one locally but don't send to server yet
    if (!requestId) {
      const newRequestId = uuidv4();
      setRequestId(newRequestId);
    }
  }, [requestId, setRequestId, useCase, setCurrentUseCase]);

  // Validation error messages based on use case
  const getValidationError = (file: File) => {
    const fileType = getFileType(file.name);
    if (fileType !== 'pdf' && fileType !== 'csv') {
      return {
        format: 'Unsupported file format. Only CSV and PDF files are accepted.',
        data: 'Please upload files in CSV or PDF format.'
      };
    }
    return null;
  };

  // Determine file type based on extension
  const getFileType = (fileName: string): 'pdf' | 'csv' => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    return extension === 'pdf' ? 'pdf' : 'csv';
  };

  // Validate file type for each use case
  const validateFileType = (file: File): boolean => {
    const fileType = getFileType(file.name);
    return fileType === 'pdf' || fileType === 'csv';
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const filesArray = Array.from(e.target.files);

    // Create temporary file entries with 'valid' status for validated files
    const newFiles = filesArray.map(file => {
      // Validate file type first
      if (!validateFileType(file)) {
        return {
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          name: file.name,
          size: file.size,
          status: 'error' as const,
          errorMessage: 'Invalid file format. Only CSV and PDF files are accepted.',
          file // Store the actual file object for later upload
        };
      }

      return {
        id: Date.now() + Math.random().toString(36).substring(2, 9),
        name: file.name,
        size: file.size,
        status: 'valid' as const,
        file // Store the actual file object for later upload
      };
    });

    // Add files to context
    addFiles(newFiles);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files.length > 0) {
      const filesArray = Array.from(e.dataTransfer.files);

      // Create temporary file entries with 'valid' status for validated files
      const newFiles = filesArray.map(file => {
        // Validate file type first
        if (!validateFileType(file)) {
          return {
            id: Date.now() + Math.random().toString(36).substring(2, 9),
            name: file.name,
            size: file.size,
            status: 'error' as const,
            errorMessage: 'Invalid file format. Only CSV and PDF files are accepted.',
            file // Store the actual file object for later upload
          };
        }

        return {
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          name: file.name,
          size: file.size,
          status: 'valid' as const,
          file // Store the actual file object for later upload
        };
      });

      // Add files to context
      addFiles(newFiles);
    }
  };

  const handleConfigChange = (key: string, value: string) => {
    updateConfig(key, value);
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    else if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    else return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Configuration panel based on use case
  const renderConfigPanel = () => {
    switch(useCase.id) {
      case 'analytical-review':
        return (
          <>
            <div className="space-y-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Materiality Threshold (%)
                </label>
                <div className="space-y-2">
                  <input
                    type="text"
                    className="input"
                    value={config.materialityThreshold}
                    onChange={(e) => handleConfigChange('materialityThreshold', e.target.value)}
                  />
                  <div className="flex items-center space-x-4 pl-1">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        className="h-4 w-4 text-accent focus:ring-accent border-gray-300"
                        checked={config.materialityLogic === 'OR'}
                        onChange={() => handleConfigChange('materialityLogic', 'OR')}
                      />
                      <span className="ml-2 text-sm text-gray-600">OR</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        className="h-4 w-4 text-accent focus:ring-accent border-gray-300"
                        checked={config.materialityLogic === 'AND'}
                        onChange={() => handleConfigChange('materialityLogic', 'AND')}
                      />
                      <span className="ml-2 text-sm text-gray-600">AND</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fixed Amount Threshold in consolidated currency
                </label>
                <div className="space-y-2">
                  <div className="relative">
                    <input
                      type="text"
                      className="input pr-16"
                      value={config.fixedAmount}
                      onChange={(e) => handleConfigChange('fixedAmount', e.target.value)}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      CHF
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Top X Results in value
                </label>
                <div className="space-y-2">
                  <input
                    type="number"
                    min="1"
                    className="input"
                    value={config.topX}
                    onChange={(e) => handleConfigChange('topX', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </>
        );
      case 'exchange-rate-review':
        return (
          <>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reference Rate Source
              </label>
              <select
                className="input"
                value={config.referenceSource}
                onChange={(e) => handleConfigChange('referenceSource', e.target.value)}
              >
                <option value="treasury">Treasury Rates</option>
                <option value="central_bank">Central Bank Rates</option>
                <option value="market">Market Rates</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Comparison Period
              </label>
              <select
                className="input"
                value={config.comparisonPeriod}
                onChange={(e) => handleConfigChange('comparisonPeriod', e.target.value)}
              >
                <option value="day">Daily</option>
                <option value="week">Weekly</option>
                <option value="month">Monthly</option>
                <option value="quarter">Quarterly</option>
              </select>
            </div>
          </>
        );
      case 'intercompany-reconciliation':
        return (
          <>
            <div className="space-y-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Materiality Threshold (%)
                </label>
                <div className="space-y-2">
                  <input
                    type="text"
                    className="input"
                    value={config.materialityThreshold}
                    onChange={(e) => handleConfigChange('materialityThreshold', e.target.value)}
                  />
                  <div className="flex items-center space-x-4 pl-1">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        className="h-4 w-4 text-accent focus:ring-accent border-gray-300"
                        checked={config.materialityLogic === 'OR'}
                        onChange={() => handleConfigChange('materialityLogic', 'OR')}
                      />
                      <span className="ml-2 text-sm text-gray-600">OR</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        className="h-4 w-4 text-accent focus:ring-accent border-gray-300"
                        checked={config.materialityLogic === 'AND'}
                        onChange={() => handleConfigChange('materialityLogic', 'AND')}
                      />
                      <span className="ml-2 text-sm text-gray-600">AND</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fixed Amount Threshold in consolidated currency
                </label>
                <div className="space-y-2">
                  <div className="relative">
                    <input
                      type="text"
                      className="input pr-16"
                      value={config.fixedAmount}
                      onChange={(e) => handleConfigChange('fixedAmount', e.target.value)}
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      CHF
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Top X Results in value
                </label>
                <div className="space-y-2">
                  <input
                    type="number"
                    min="1"
                    className="input"
                    value={config.topX}
                    onChange={(e) => handleConfigChange('topX', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </>
        );
      case 'rounding-issue-resolution':
        return (
          <>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rounding Rule
              </label>
              <select
                className="input"
                value={config.roundingRule}
                onChange={(e) => handleConfigChange('roundingRule', e.target.value)}
              >
                <option value="nearest">Round to Nearest</option>
                <option value="up">Round Up</option>
                <option value="down">Round Down</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Correction Priority
              </label>
              <select
                className="input"
                value={config.correctionPriority}
                onChange={(e) => handleConfigChange('correctionPriority', e.target.value)}
              >
                <option value="totals">Preserve Totals</option>
                <option value="integrity">Cross-Reference Integrity</option>
                <option value="minimal">Minimal Changes</option>
              </select>
            </div>
          </>
        );
      default:
        return null;
    }
  };

  const isProcessable = files.some(file => file.status === 'valid');

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* Instructions Panel */}
      <div className="h-full">
        <Card variant="bordered">
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="space-y-4 text-sm text-gray-700 list-decimal pl-5">
              {useCase.id === 'rounding-issue-resolution' ? (
                <>
                  <li>Prepare your data files in CSV or PDF format</li>
                  <li>Upload your files via drag and drop or browse</li>
                  <li>Click 'Process Data' to start analysis</li>
                </>
              ) : (
                <>
                  <li>Prepare your data files in CSV or PDF format</li>
                  <li>Upload your files via drag and drop or browse</li>
                  {useCase.id !== 'exchange-rate-review' && (
                    <li>Set configuration parameters as needed</li>
                  )}
                  <li>Click 'Process Data' to start analysis</li>
                </>
              )}
            </ol>
          </CardContent>
        </Card>
      </div>

      {/* Upload Area */}
      <div className="h-full">
        <Card variant="bordered" className="h-full flex flex-col">
          <CardHeader>
            <CardTitle>{useCase.id === 'rounding-issue-resolution' ? 'Financial Statements Files' : useCase.id === 'exchange-rate-review' ? 'Upload System Rate Files' : 'Upload Data Files'}</CardTitle>
          </CardHeader>
          <CardContent className="flex-grow flex flex-col">
            {/* Drag & Drop Zone */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center mb-4 flex-grow ${
                isDragging ? 'border-accent bg-accent-50' : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="p-3 bg-gray-100 rounded-full mb-4">
                <Upload className="h-6 w-6 text-gray-500" />
              </div>
              <p className="text-center mb-2">
                <span className="font-medium">Drag files here</span> or <span className="text-accent">browse</span>
              </p>
              <p className="text-sm text-gray-500 text-center mb-4">
                Supported formats: CSV, PDF
              </p>
              <input
                type="file"
                id="file-upload"
                className="hidden"
                multiple
                accept=".csv,.pdf"
                onChange={handleFileUpload}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                Select Files
              </Button>
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">File Name</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Size</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">Status</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">Action</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {files.map(file => (
                      <tr key={file.id}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 max-w-0">
                          <div className="truncate" title={file.name}>
                            {file.name}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          {file.status === 'uploading' && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Uploading...
                            </span>
                          )}
                          {file.status === 'validating' && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              Validating...
                            </span>
                          )}
                          {file.status === 'valid' && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Valid
                            </span>
                          )}
                          {file.status === 'error' && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              Error
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => removeFile(file.id)}
                            className="text-red-600 hover:text-red-800"
                            aria-label="Remove file"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Validation Errors */}
            {files.some(file => file.status === 'error') && (
              <div className="mt-4 p-3 bg-red-50 rounded-md">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-800">Validation Errors</p>
                    <ul className="mt-1 text-sm text-red-700 list-disc pl-5">
                      {files
                        .filter(file => file.status === 'error')
                        .map(file => (
                          <li key={`error-${file.id}`} className="mb-2">
                            <span className="font-medium truncate inline-block max-w-xs">{file.name}:</span> {file.errorMessage}
                          </li>
                        ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Reference Source Rates Upload Area - Only for Exchange Rate Review */}
      {useCase.id === 'exchange-rate-review' && (
        <div>
          <Card variant="bordered" className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Upload Reference Source Rates Files</CardTitle>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col">
              {/* Drag & Drop Zone */}
              <div
                className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center mb-4 flex-grow ${
                  isDragging ? 'border-accent bg-accent-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="p-3 bg-gray-100 rounded-full mb-4">
                  <Upload className="h-6 w-6 text-gray-500" />
                </div>
                <p className="text-center mb-2">
                  <span className="font-medium">Drag files here</span> or <span className="text-accent">browse</span>
                </p>
                <p className="text-sm text-gray-500 text-center mb-4">
                  Supported formats: CSV, PDF
                </p>
                <input
                  type="file"
                  id="reference-file-upload"
                  className="hidden"
                  multiple
                  accept=".csv,.pdf"
                  onChange={handleFileUpload}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('reference-file-upload')?.click()}
                >
                  Select Files
                </Button>
              </div>

              {/* File List */}
              {files.length > 0 && (
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">File Name</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Size</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">Status</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">Action</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {files.map(file => (
                        <tr key={file.id}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 max-w-0">
                            <div className="truncate" title={file.name}>
                              {file.name}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {file.status === 'uploading' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Uploading...
                              </span>
                            )}
                            {file.status === 'validating' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Validating...
                              </span>
                            )}
                            {file.status === 'valid' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Valid
                              </span>
                            )}
                            {file.status === 'error' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Error
                              </span>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => removeFile(file.id)}
                              className="text-red-600 hover:text-red-800"
                              aria-label="Remove file"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Financial Statement Rounding Rules Upload Area - Only for Rounding Issue Resolution */}
      {useCase.id === 'rounding-issue-resolution' && (
        <div className="h-full">
          <Card variant="bordered" className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Financial Statement Rounding Rules</CardTitle>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col">
              {/* Drag & Drop Zone */}
              <div
                className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center mb-4 flex-grow ${
                  isDragging ? 'border-accent bg-accent-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="p-3 bg-gray-100 rounded-full mb-4">
                  <Upload className="h-6 w-6 text-gray-500" />
                </div>
                <p className="text-center mb-2">
                  <span className="font-medium">Drag files here</span> or <span className="text-accent">browse</span>
                </p>
                <p className="text-sm text-gray-500 text-center mb-4">
                  Supported formats: CSV, PDF
                </p>
                <input
                  type="file"
                  id="rules-file-upload"
                  className="hidden"
                  multiple
                  accept=".csv,.pdf"
                  onChange={handleFileUpload}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('rules-file-upload')?.click()}
                >
                  Select Files
                </Button>
              </div>

              {/* File List */}
              {files.length > 0 && (
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">File Name</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">Size</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">Status</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">Action</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {files.map(file => (
                        <tr key={file.id}>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 max-w-0">
                            <div className="truncate" title={file.name}>
                              {file.name}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {file.status === 'uploading' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Uploading...
                              </span>
                            )}
                            {file.status === 'validating' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Validating...
                              </span>
                            )}
                            {file.status === 'valid' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Valid
                              </span>
                            )}
                            {file.status === 'error' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Error
                              </span>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => removeFile(file.id)}
                              className="text-red-600 hover:text-red-800"
                              aria-label="Remove file"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Configuration Panel */}
      {useCase.id !== 'exchange-rate-review' && useCase.id !== 'rounding-issue-resolution' && (
      <div className="lg:col-span-1">
        <Card variant="bordered">
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            {renderConfigPanel()}

            <div className="mt-6 pt-4 border-t border-gray-200">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Save as template</span>
              </label>
            </div>
          </CardContent>
        </Card>
      </div>
      )}
    </div>
  );
}