import { useState } from 'react';
import { Mail, AlertTriangle, CheckCircle, Calendar, User, MailCheck, Edit, Clock } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import { UseCase } from '../../../data/useCases';

interface CommunicationStepProps {
  useCase: UseCase;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
}

interface Recipient {
  id: string;
  name: string;
  email: string;
  entity: string;
  issues: number;
  selected: boolean;
}

export default function CommunicationStep({ useCase }: CommunicationStepProps) {
  // Mock email templates
  const [templates] = useState<EmailTemplate[]>([
    {
      id: '1',
      name: 'Standard Variance Report',
      subject: '[Orinkia] Financial Variance Report - Action Required',
      body: 'Dear {{recipient}},\n\nOur automated financial review has identified {{issues}} significant variances in {{entity}} financial data that require your attention.\n\nPlease review and provide explanations for these variances by {{dueDate}}.\n\nYou can access the detailed report at: {{reportLink}}\n\nRegards,\nOrinkia Felix AI'
    },
    {
      id: '2',
      name: 'Urgent Reconciliation Request',
      subject: '[Orinkia] URGENT: Intercompany Reconciliation Required',
      body: 'Dear {{recipient}},\n\nOur system has identified {{issues}} critical intercompany reconciliation issues that require immediate attention.\n\nPlease review and resolve these discrepancies by {{dueDate}}.\n\nYou can access the detailed report at: {{reportLink}}\n\nRegards,\nOrinkia Felix AI'
    },
    {
      id: '3',
      name: 'Monthly Review Notification',
      subject: '[Orinkia] Monthly Financial Review - Issues Detected',
      body: 'Dear {{recipient}},\n\nAs part of our monthly financial review process, we have identified {{issues}} items requiring your review for {{entity}}.\n\nPlease provide your feedback by {{dueDate}}.\n\nYou can access the detailed report at: {{reportLink}}\n\nRegards,\nOrinkia Felix AI'
    }
  ]);
  
  // Selected template
  const [selectedTemplateId, setSelectedTemplateId] = useState(templates[0].id);
  
  // Mock recipients
  const [recipients, setRecipients] = useState<Recipient[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      entity: 'UK Subsidiary',
      issues: 4,
      selected: true
    },
    {
      id: '2',
      name: 'Maria Garcia',
      email: '<EMAIL>',
      entity: 'FR Subsidiary',
      issues: 3,
      selected: true
    },
    {
      id: '3',
      name: 'Thomas Weber',
      email: '<EMAIL>',
      entity: 'DE Subsidiary',
      issues: 2,
      selected: true
    },
    {
      id: '4',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      entity: 'US Subsidiary',
      issues: 3,
      selected: true
    }
  ]);
  
  // Email settings
  const [emailSettings, setEmailSettings] = useState({
    subject: '',
    dueDate: '2025-04-15',
    ccFinance: true,
    attachReport: true,
    scheduleTime: ''
  });
  
  // Set email subject based on selected template
  useState(() => {
    const template = templates.find(t => t.id === selectedTemplateId);
    if (template) {
      setEmailSettings(prev => ({ ...prev, subject: template.subject }));
    }
  });
  
  // Calculate email stats
  const emailStats = {
    total: recipients.filter(r => r.selected).length,
    issues: recipients.reduce((sum, r) => r.selected ? sum + r.issues : sum, 0)
  };
  
  // Toggle recipient selection
  const toggleRecipient = (id: string) => {
    setRecipients(prev => 
      prev.map(r => r.id === id ? { ...r, selected: !r.selected } : r)
    );
  };
  
  // Handle template selection
  const handleTemplateChange = (id: string) => {
    setSelectedTemplateId(id);
    const template = templates.find(t => t.id === id);
    if (template) {
      setEmailSettings(prev => ({ ...prev, subject: template.subject }));
    }
  };
  
  // Get selected template
  const selectedTemplate = templates.find(t => t.id === selectedTemplateId) || templates[0];
  
  // Preview email body with replacements
  const getPreviewBody = (recipient: Recipient) => {
    return selectedTemplate.body
      .replace('{{recipient}}', recipient.name)
      .replace('{{issues}}', recipient.issues.toString())
      .replace('{{entity}}', recipient.entity)
      .replace('{{dueDate}}', new Date(emailSettings.dueDate).toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      }))
      .replace('{{reportLink}}', 'https://app.orinkia.com/reports/12345');
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Email Template Section */}
      <div className="lg:col-span-1">
        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="h-5 w-5 mr-2 text-accent" />
              Email Template
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Template
              </label>
              <select
                value={selectedTemplateId}
                onChange={(e) => handleTemplateChange(e.target.value)}
                className="input"
              >
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subject Line
              </label>
              <input
                type="text"
                value={emailSettings.subject}
                onChange={(e) => setEmailSettings(prev => ({ ...prev, subject: e.target.value }))}
                className="input"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Response Due Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={emailSettings.dueDate}
                  onChange={(e) => setEmailSettings(prev => ({ ...prev, dueDate: e.target.value }))}
                  className="input pl-10"
                />
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message Preview
              </label>
              <div className="border border-gray-300 rounded-md p-3 bg-gray-50 text-sm whitespace-pre-line h-40 overflow-y-auto">
                {getPreviewBody(recipients[0])}
              </div>
            </div>
            
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={emailSettings.ccFinance}
                  onChange={() => setEmailSettings(prev => ({ ...prev, ccFinance: !prev.ccFinance }))}
                  className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">CC Finance Department</span>
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={emailSettings.attachReport}
                  onChange={() => setEmailSettings(prev => ({ ...prev, attachReport: !prev.attachReport }))}
                  className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Attach PDF Report</span>
              </label>
            </div>
            
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Schedule Sending
              </label>
              <div className="relative">
                <input
                  type="time"
                  value={emailSettings.scheduleTime}
                  onChange={(e) => setEmailSettings(prev => ({ ...prev, scheduleTime: e.target.value }))}
                  className="input pl-10"
                  placeholder="Send immediately"
                />
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              </div>
              <p className="text-xs text-gray-500 mt-1">Leave empty to send immediately</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Recipients Section */}
      <div className="lg:col-span-2">
        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2 text-accent" />
              Recipients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
              <div className="flex space-x-4">
                <div className="bg-accent bg-opacity-10 p-2 rounded-md">
                  <div className="text-sm text-gray-500">Selected Recipients</div>
                  <div className="text-lg font-semibold text-primary">{emailStats.total}</div>
                </div>
                <div className="bg-accent bg-opacity-10 p-2 rounded-md">
                  <div className="text-sm text-gray-500">Total Issues</div>
                  <div className="text-lg font-semibold text-primary">{emailStats.issues}</div>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Edit size={16} />}
              >
                Edit Recipients
              </Button>
            </div>
            
            <div className="border border-gray-200 rounded-lg overflow-hidden mb-6">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-12 px-4 py-3">
                      <input
                        type="checkbox"
                        checked={recipients.every(r => r.selected)}
                        onChange={() => {
                          const allSelected = recipients.every(r => r.selected);
                          setRecipients(prev => 
                            prev.map(r => ({ ...r, selected: !allSelected }))
                          );
                        }}
                        className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                      />
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entity</th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Issues</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preview</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recipients.map(recipient => (
                    <tr key={recipient.id} className={recipient.selected ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-4 py-4">
                        <input
                          type="checkbox"
                          checked={recipient.selected}
                          onChange={() => toggleRecipient(recipient.id)}
                          className="h-4 w-4 text-accent focus:ring-accent border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-4 py-4">
                        <div className="font-medium text-gray-900">{recipient.name}</div>
                        <div className="text-gray-500 text-sm">{recipient.email}</div>
                      </td>
                      <td className="px-4 py-4 text-sm">{recipient.entity}</td>
                      <td className="px-4 py-4 text-center">
                        <span className="bg-accent bg-opacity-10 text-accent py-1 px-2 rounded-full text-sm">
                          {recipient.issues}
                        </span>
                      </td>
                      <td className="px-4 py-4">
                        <Button variant="ghost" size="sm">
                          View
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Compliance reminder and send button */}
            <div className="flex justify-end">
              <Button
                size="lg"
                leftIcon={<MailCheck size={18} />}
              >
                Send {emailStats.total} Emails
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {/* Email Preview Card */}
        <Card variant="bordered" className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              What Recipients Will See
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <div className="border-b border-gray-200 pb-3 mb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-sm text-gray-500">From:</div>
                    <div className="text-sm font-medium">Orinkia Felix AI &lt;<EMAIL>&gt;</div>
                  </div>
                  <div className="text-xs text-gray-400">Apr 1, 2025, 10:30 AM</div>
                </div>
                <div className="mt-2">
                  <div className="text-sm text-gray-500">To:</div>
                  <div className="text-sm font-medium">{recipients[0].name} &lt;{recipients[0].email}&gt;</div>
                </div>
                <div className="mt-2">
                  <div className="text-sm text-gray-500">Subject:</div>
                  <div className="text-sm font-medium">{emailSettings.subject}</div>
                </div>
              </div>
              
              <div className="text-sm whitespace-pre-line">
                {getPreviewBody(recipients[0])}
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-400">
                This email was generated by Orinkia AI Solutions. Please do not reply directly to this email.
              </div>
              
              {emailSettings.attachReport && (
                <div className="mt-3 flex items-center bg-gray-50 rounded p-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-amber-500 mr-2" />
                  <span>PDF Report will be attached</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}