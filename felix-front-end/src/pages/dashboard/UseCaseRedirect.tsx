import { useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { getUseCaseById } from '../../data/useCases';

/**
 * UseCaseRedirect component handles redirecting from the old use case URL format
 * to the new individual use case pages.
 */
export default function UseCaseRedirect() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!id) {
      // If no ID is provided, redirect to dashboard
      navigate('/dashboard');
      return;
    }

    const useCase = getUseCaseById(id);
    if (!useCase) {
      // If the use case doesn't exist, redirect to dashboard
      navigate('/dashboard');
      return;
    }

    // Extract the path after the ID to preserve the step
    const pathMatch = location.pathname.match(new RegExp(`/dashboard/use-case/${id}(.*)`));
    const stepPath = pathMatch && pathMatch[1] ? pathMatch[1] : '';

    console.log(`[UseCaseRedirect] Redirecting to specific use case page with path: ${stepPath}`);

    // Redirect to the specific use case page, preserving the step path
    navigate(`/dashboard/use-case/${id}${stepPath}`, { replace: true });
  }, [id, navigate, location.pathname]);

  return (
    <div className="p-6">
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Redirecting...</h2>
        <p className="text-gray-600 mb-4">Please wait while we redirect you to the requested use case.</p>
      </div>
    </div>
  );
}
