import { ChevronRight } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { useCases } from '../../data/useCases';
import { clearAllUseCaseData } from '../../utils/sessionStorage';
import { useEffect } from 'react';

// Dummy data for active processes
const activeProcesses = {
  total: 3,
  uploadedFiles: 127,
  pendingReviews: 2,
  issuesRequiringAttention: 1
};

// Dummy data for recent activity
const recentActivity = [
  {
    id: '1',
    type: 'analytical-review',
    startTime: '2025-04-01T09:15:00Z',
    completionTime: '2025-04-01T09:30:00Z',
    status: 'completed'
  },
  {
    id: '2',
    type: 'exchange-rate-review',
    startTime: '2025-03-31T14:45:00Z',
    completionTime: '2025-03-31T15:00:00Z',
    status: 'completed'
  },
  {
    id: '3',
    type: 'intercompany-reconciliation',
    startTime: '2025-03-30T10:10:00Z',
    completionTime: '2025-03-30T10:30:00Z',
    status: 'completed'
  },
  {
    id: '4',
    type: 'rounding-issue-resolution',
    startTime: '2025-03-29T16:20:00Z',
    completionTime: '2025-03-29T16:35:00Z',
    status: 'completed'
  },
  {
    id: '5',
    type: 'analytical-review',
    startTime: '2025-03-28T11:00:00Z',
    completionTime: '2025-03-28T11:15:00Z',
    status: 'completed'
  }
];

export default function Dashboard() {
  const { user } = useAuth();
  const currentDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date());

  // Clear any existing use case data when the dashboard loads
  useEffect(() => {
    clearAllUseCaseData();
    console.log('[Dashboard] Cleared all use case data on dashboard load');
  }, []);

  const renderIcon = (type: string) => {
    const useCase = useCases.find(uc => uc.id === type);
    if (!useCase) return null;

    const IconComponent = useCase ? icons[useCase.icon] : null;
    if (!IconComponent) return null;

    return <IconComponent className="h-5 w-5 text-primary" />;
  };

  // Ensure all the icons are imported
  const icons = {
    BarChart3: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="3" height="10" x="3" y="10" rx="1"/><rect width="3" height="18" x="10" y="2" rx="1"/><rect width="3" height="14" x="17" y="6" rx="1"/></svg>,
    CircleDollarSign: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"/><path d="M12 18V6"/></svg>,
    Network: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="16" y="16" width="6" height="6" rx="1"/><rect x="2" y="16" width="6" height="6" rx="1"/><rect x="9" y="2" width="6" height="6" rx="1"/><path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"/><path d="M12 12V8"/></svg>,
    Calculator: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="16" height="20" x="4" y="2" rx="2"/><line x1="8" x2="16" y1="6" y2="6"/><line x1="16" x2="16" y1="14" y2="18"/><path d="M16 10h.01"/><path d="M12 10h.01"/><path d="M8 10h.01"/><path d="M12 14h.01"/><path d="M8 14h.01"/><path d="M12 18h.01"/><path d="M8 18h.01"/></svg>
  };

  const formatDateTime = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">Welcome, {user?.name?.split(' ')[0]}</h1>
        <p className="text-gray-600">{currentDate}</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-600">Uploaded Files</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">{activeProcesses.uploadedFiles}</div>
            <Link to="/dashboard/active-processes" className="text-accent flex items-center text-sm mt-2">
              View details <ChevronRight size={16} />
            </Link>
          </CardContent>
        </Card>

        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-600">Active Processes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">{activeProcesses.total}</div>
            <Link to="/dashboard/active-processes" className="text-accent flex items-center text-sm mt-2">
              View details <ChevronRight size={16} />
            </Link>
          </CardContent>
        </Card>

        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-600">Pending Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">{activeProcesses.pendingReviews}</div>
            <Link to="/dashboard/active-processes" className="text-accent flex items-center text-sm mt-2">
              View details <ChevronRight size={16} />
            </Link>
          </CardContent>
        </Card>

        <Card variant="bordered">
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-600">Issues Requiring Attention</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">{activeProcesses.issuesRequiringAttention}</div>
            <Link to="/dashboard/active-processes" className="text-accent flex items-center text-sm mt-2">
              View details <ChevronRight size={16} />
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Use Cases */}
      <h2 className="text-xl font-semibold mb-4">Use Cases</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {useCases.map((useCase) => {
          const IconComponent = icons[useCase.icon as keyof typeof icons];

          return (
            <Card key={useCase.id} variant="bordered" className="hover:border-accent transition-colors">
              <CardHeader className="flex flex-row items-start space-y-0 pb-2">
                <div className="mr-2 p-1.5 rounded-md bg-primary-50">
                  <IconComponent className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="font-semibold">{useCase.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-3">{useCase.description}</p>
                <div className="flex items-start justify-between">
                  <Link
                    to={`/dashboard/use-case/${useCase.id}`}
                    onClick={() => {
                      // Clear any existing data for this use case when starting fresh
                      clearAllUseCaseData();
                      console.log(`[Dashboard] Starting fresh process for ${useCase.id}`);
                    }}
                  >
                    <Button variant="primary" size="sm">Start Process</Button>
                  </Link>
                  <div className="flex-1 ml-4">
                <p className="text-sm mb-4">
                  <span className="font-medium text-gray-700">Your Input:</span>
                  <span className="text-gray-600"> {useCase.input}</span>
                </p>
                <p className="text-sm mb-4">
                  <span className="font-medium text-gray-700">Felix Value:</span>
                  <span className="text-gray-600"> {useCase.value}</span>
                </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity */}
      <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
      <Card variant="bordered">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {recentActivity.map((activity) => {
                  const useCase = useCases.find(uc => uc.id === activity.type);

                  return (
                    <tr key={activity.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="mr-2">
                            {renderIcon(activity.type)}
                          </div>
                          <span className="font-medium text-gray-700">{useCase?.name || activity.type}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {formatDateTime(activity.startTime)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {formatDateTime(activity.completionTime)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 capitalize">
                          {activity.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <Button variant="ghost" size="sm">
                          View Results
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}