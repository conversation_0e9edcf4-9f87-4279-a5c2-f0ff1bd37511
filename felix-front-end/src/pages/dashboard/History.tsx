import { useState } from 'react';
import { CalendarIcon, DownloadIcon, Filter, Search } from 'lucide-react';
import { Card, CardContent } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { useCases } from '../../data/useCases';

// Dummy data for historical processes
const historicalProcesses = [
  {
    id: '901',
    type: 'analytical-review',
    dateTime: '2025-03-15T14:30:00Z',
    user: '<PERSON>',
    entities: ['Global HQ', 'UK Subsidiary', 'FR Subsidiary', 'DE Subsidiary'],
    issuesFound: 12,
    resolutionRate: '92%'
  },
  {
    id: '875',
    type: 'exchange-rate-review',
    dateTime: '2025-03-01T10:15:00Z',
    user: '<PERSON>',
    entities: ['All Subsidiaries'],
    issuesFound: 3,
    resolutionRate: '100%'
  },
  {
    id: '843',
    type: 'intercompany-reconciliation',
    dateTime: '2025-02-15T09:45:00Z',
    user: '<PERSON>',
    entities: ['UK Subsidiary', 'US Subsidiary', 'CA Subsidiary'],
    issuesFound: 8,
    resolutionRate: '87%'
  },
  {
    id: '821',
    type: 'rounding-issue-resolution',
    dateTime: '2025-02-01T11:30:00Z',
    user: 'Thomas Weber',
    entities: ['All Entities'],
    issuesFound: 4,
    resolutionRate: '100%'
  },
  {
    id: '795',
    type: 'analytical-review',
    dateTime: '2025-01-15T15:20:00Z',
    user: 'John Doe',
    entities: ['Global HQ', 'UK Subsidiary', 'FR Subsidiary'],
    issuesFound: 15,
    resolutionRate: '93%'
  }
];

// Dummy data for KPI metrics
const kpiMetrics = {
  dataProcessed: '1.2 TB',
  avgProcessingTime: '12 minutes',
  issueDetectionRate: '97%',
  resolutionRate: '94%',
  timeSaved: '76 hours/month'
};

// Dummy data for trend analysis
const trendData = {
  months: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
  issueCategories: {
    'Variance Analysis': [8, 12, 9, 11, 12],
    'Exchange Rates': [5, 3, 4, 3, 3],
    'Intercompany': [10, 7, 9, 7, 8],
    'Rounding': [3, 5, 2, 4, 4]
  },
  processingEfficiency: [82, 85, 88, 91, 95],
  entityPerformance: {
    'Global HQ': [92, 94, 93, 95, 96],
    'UK Subsidiary': [85, 87, 89, 93, 94],
    'US Subsidiary': [83, 84, 88, 90, 91],
    'FR Subsidiary': [80, 82, 87, 89, 93]
  }
};

// Icons mapping for types
const icons = {
  BarChart3: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="3" height="10" x="3" y="10" rx="1"/><rect width="3" height="18" x="10" y="2" rx="1"/><rect width="3" height="14" x="17" y="6" rx="1"/></svg>,
  CircleDollarSign: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"/><path d="M12 18V6"/></svg>,
  Network: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="16" y="16" width="6" height="6" rx="1"/><rect x="2" y="16" width="6" height="6" rx="1"/><rect x="9" y="2" width="6" height="6" rx="1"/><path d="M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3"/><path d="M12 12V8"/></svg>,
  Calculator: (props: any) => <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="16" height="20" x="4" y="2" rx="2"/><line x1="8" x2="16" y1="6" y2="6"/><line x1="16" x2="16" y1="14" y2="18"/><path d="M16 10h.01"/><path d="M12 10h.01"/><path d="M8 10h.01"/><path d="M12 14h.01"/><path d="M8 14h.01"/><path d="M12 18h.01"/><path d="M8 18h.01"/></svg>
};

export default function History() {
  const [activeTab, setActiveTab] = useState('archive');

  const renderIcon = (type: string) => {
    const useCase = useCases.find(uc => uc.id === type);
    if (!useCase) return null;
    
    const IconComponent = icons[useCase.icon as keyof typeof icons];
    if (!IconComponent) return null;
    
    return <IconComponent className="h-5 w-5 text-primary" />;
  };

  const formatDateTime = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">History & Analytics</h1>
        <p className="text-gray-600">View past processes and performance metrics</p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'archive'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('archive')}
        >
          Process Archive
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'performance'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('performance')}
        >
          Performance Metrics
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm focus:outline-none ${
            activeTab === 'trends'
              ? 'text-primary border-b-2 border-primary'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('trends')}
        >
          Trend Analysis
        </button>
      </div>

      {activeTab === 'archive' && (
        <>
          {/* Archive Controls */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
            <div className="relative w-full md:w-64">
              <input
                type="text"
                placeholder="Search processes..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Button
                variant="outline"
                size="sm"
                leftIcon={<CalendarIcon size={16} />}
              >
                Date Range
              </Button>
              <Button
                variant="outline"
                size="sm"
                leftIcon={<Filter size={16} />}
              >
                Filter
              </Button>
            </div>
          </div>

          {/* Process Archive Table */}
          <Card variant="bordered">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Run ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entities</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issues Found</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resolution Rate</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {historicalProcesses.map((process) => {
                      const useCase = useCases.find(uc => uc.id === process.type);
                      
                      return (
                        <tr key={process.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {process.id}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="mr-2">
                                {renderIcon(process.type)}
                              </div>
                              <span className="font-medium text-gray-700">{useCase?.name || process.type}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {formatDateTime(process.dateTime)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.user}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.entities.length > 1 ? 
                              `${process.entities[0]} +${process.entities.length - 1} more` : 
                              process.entities[0]
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.issuesFound}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {process.resolutionRate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div className="flex justify-end space-x-2">
                              <Button variant="ghost" size="sm">
                                View Report
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<DownloadIcon size={14} />}
                                className="px-2"
                              >
                                Export
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {activeTab === 'performance' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Data Processed</div>
                <div className="text-2xl font-bold text-primary">{kpiMetrics.dataProcessed}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Avg. Processing Time</div>
                <div className="text-2xl font-bold text-primary">{kpiMetrics.avgProcessingTime}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Issue Detection Rate</div>
                <div className="text-2xl font-bold text-primary">{kpiMetrics.issueDetectionRate}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Resolution Rate</div>
                <div className="text-2xl font-bold text-primary">{kpiMetrics.resolutionRate}</div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-4">
                <div className="text-sm text-gray-500 mb-1">Time Saved</div>
                <div className="text-2xl font-bold text-primary">{kpiMetrics.timeSaved}</div>
              </CardContent>
            </Card>
          </div>
          
          <div className="mb-6">
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Performance Overview</h3>
                <p className="text-gray-600 mb-6">
                  This dashboard shows key performance metrics for Felix's AI processing capabilities. 
                  The system has maintained a high issue detection rate of {kpiMetrics.issueDetectionRate} while 
                  sustaining an excellent resolution rate of {kpiMetrics.resolutionRate}. 
                  The average processing time has improved from 18 minutes to {kpiMetrics.avgProcessingTime} 
                  in the last quarter.
                </p>
                
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Performance metrics visualization would appear here</p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Processing Efficiency by Entity</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Entity efficiency chart would appear here</p>
                </div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Issue Resolution by Type</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Issue resolution chart would appear here</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {activeTab === 'trends' && (
        <>
          <div className="mb-6">
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Issues by Category Over Time</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Issues by category trend chart would appear here</p>
                </div>
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(trendData.issueCategories).map(([category, values]) => (
                    <div key={category} className="text-center">
                      <div className="text-sm text-gray-500 mb-1">{category}</div>
                      <div className="text-lg font-semibold text-primary">
                        {values[values.length - 1]} <span className="text-sm text-gray-400">issues</span>
                      </div>
                      <div className={`text-xs ${values[values.length - 1] < values[values.length - 2] ? 'text-green-500' : 'text-red-500'}`}>
                        {values[values.length - 1] < values[values.length - 2] ? '↓' : '↑'} 
                        {Math.abs(values[values.length - 1] - values[values.length - 2])} from last month
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Processing Efficiency Trend</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Processing efficiency trend chart would appear here</p>
                </div>
                <div className="mt-4 text-center">
                  <div className="text-sm text-gray-500 mb-1">Current Efficiency</div>
                  <div className="text-lg font-semibold text-primary">
                    {trendData.processingEfficiency[trendData.processingEfficiency.length - 1]}%
                  </div>
                  <div className="text-xs text-green-500">
                    ↑ {trendData.processingEfficiency[trendData.processingEfficiency.length - 1] - trendData.processingEfficiency[trendData.processingEfficiency.length - 2]}% from last month
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card variant="bordered">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4">Entity Performance Comparison</h3>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Entity performance comparison chart would appear here</p>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-4">
                  {Object.entries(trendData.entityPerformance).slice(0, 2).map(([entity, values]) => (
                    <div key={entity} className="text-center">
                      <div className="text-sm text-gray-500 mb-1">{entity}</div>
                      <div className="text-lg font-semibold text-primary">
                        {values[values.length - 1]}%
                      </div>
                      <div className="text-xs text-green-500">
                        ↑ {values[values.length - 1] - values[values.length - 2]}% from last month
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card variant="bordered">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Month-over-Month Improvement</h3>
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Month-over-month improvement chart would appear here</p>
              </div>
              <p className="mt-4 text-gray-600">
                The data shows consistent month-over-month improvements across all key metrics. 
                Most notably, processing efficiency has improved from {trendData.processingEfficiency[0]}% to 
                {trendData.processingEfficiency[trendData.processingEfficiency.length - 1]}% over the last 
                {trendData.months.length} months, while the UK and FR subsidiaries have shown the most dramatic 
                improvements in performance.
              </p>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}