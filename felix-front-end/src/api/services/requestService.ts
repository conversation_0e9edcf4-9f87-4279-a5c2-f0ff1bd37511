import api from '../axiosConfig';
import { getUserId } from '../../auth/authService';
import config from '../../config';

export interface RequestStatus {
  DATA_INCOMPLETE: 'DATA_INCOMPLETE';
  READY_TO_PROCESS: 'READY_TO_PROCESS';
  PROCESSING: 'PROCESSING';
  DONE: 'DONE';
}

export interface RequestCreate {
  user_id: string;
  status?: keyof RequestStatus;
}

export interface RequestUpdate {
  status?: keyof RequestStatus;
}

export interface StatusStreamCallbacks {
  onOpen?: () => void;
  onMessage?: (message: string) => void;
  onError?: (error: any) => void;
  onCompleted?: () => void;
}

export interface ProcessRequest {
  request_id: string;
}


/**
 * Service for handling request operations
 */
const requestService = {
  /**
   * Creates a new request in the system
   * @param providedUserId - The ID of the user creating the request (optional, will use current user if not provided)
   * @returns The response from the server
   */
  async createRequest(providedUserId?: string): Promise<any> {
    try {
      // Use provided user ID or get from auth service
      const userId = providedUserId || getUserId();

      if (!userId) {
        console.error('User ID not available. Please ensure you are logged in.');
        throw new Error('Authentication required. Please log in and try again.');
      }

      const requestData: RequestCreate = {
        user_id: userId,
        status: 'DATA_INCOMPLETE'
      };

      console.log('Creating request with user ID:', requestData.user_id);
      const response = await api.post('/api/v1/request', requestData);

      // Log the response for debugging
      console.log('Create request response:', response.data);

      // Return the response data
      return response.data;
    } catch (error) {
      console.error('Error creating request:', error);
      throw error;
    }
  },

  /**
   * Updates the status of a request
   * @param requestId - The ID of the request to update
   * @param status - The new status of the request
   * @returns The response from the server
   */
  async updateRequestStatus(requestId: string, status: keyof RequestStatus): Promise<any> {
    try {
      const requestData: RequestUpdate = {
        status
      };

      console.log(`Updating request ${requestId} status to ${status}`);
      const response = await api.put(`/api/v1/request/${requestId}`, requestData);
      console.log('Update request status response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating request status:', error);
      throw error;
    }
  },

  /**
   * Gets a request by ID
   * @param requestId - The ID of the request to retrieve
   * @returns The response from the server
   */
  async getRequest(requestId: string): Promise<any> {
    try {
      console.log(`Getting request ${requestId}`);
      const response = await api.get(`/api/v1/request/${requestId}`);
      console.log('Get request response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting request:', error);
      throw error;
    }
  },

  /**
   * Gets the results of a processed request by calling the process API
   * @param requestId - The ID of the request to get results for
   * @returns The response from the server containing the results
   */
  async getResults(requestId: string): Promise<any> {
    try {
      console.log(`Getting results for request ${requestId}`);

      const requestData: ProcessRequest = {
        request_id: requestId
      };

      // The axiosConfig automatically adds the Authorization header with the token
      const response = await api.post('/api/v1/felix-ai-api/process', requestData);

      console.log('Get results response:', response.data);
      return response.data;
    } catch (error: any) {
      // Detailed error handling for better debugging
      let errorMessage = 'Error getting results';

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        errorMessage = `Server error (${status}): ${message}`;

        // Add specific handling for common errors
        if (status === 401) {
          errorMessage = 'Authentication failed. Please sign in again.';
          console.error('Authentication error when getting results:', error.response.data);
        } else if (status === 400) {
          errorMessage = `Bad request: ${message}`;
          console.error('Bad request error:', error.response.data);
        } else if (status === 404) {
          errorMessage = `Results not found: ${message}`;
          console.error('Results not found:', error.response.data);
        }
      } else if (error.request) {
        // Request made but no response received
        errorMessage = 'No response received from server. Please check your connection and try again.';
      }

      console.error('Error getting results:', errorMessage, error);
      throw new Error(errorMessage);
    }
  },

  /**
   * Gets the URL for the status stream of a request
   * @param requestId - The ID of the request to get the status stream for
   * @returns The URL for the status stream
   */
  getStatusStreamUrl(requestId: string): string {
    return `${config.api.baseUrl}/api/v1/request/status/stream/${requestId}`;
  },

  /**
   * Connects to the status stream of a request and registers callbacks for the events
   * @param requestId - The ID of the request to connect to
   * @param callbacks - The callbacks to register for the events
   * @returns A cleanup function that closes the connection
   */
  connectToStatusStream(requestId: string, callbacks: StatusStreamCallbacks): () => void {
    // Get the status stream URL
    const eventSourceUrl = this.getStatusStreamUrl(requestId);
    console.log(`[RequestService] Connecting to EventSource: ${eventSourceUrl}`);

    // Create the EventSource
    const eventSource = new EventSource(eventSourceUrl);

    // Register the open event handler
    eventSource.onopen = () => {
      console.log('[RequestService] EventSource connection opened.');
      if (callbacks.onOpen) {
        callbacks.onOpen();
      }
    };

    // Register the message event handler
    eventSource.onmessage = (event) => {
      console.log('[RequestService] Received message:', event.data);
      try {
        // Try to parse the message as JSON
        let statusMessage = event.data;
        try {
          const parsedData = JSON.parse(event.data);
          if (parsedData && typeof parsedData.status === 'string') {
            statusMessage = parsedData.status;
          } else if (parsedData && typeof parsedData.message === 'string') {
            statusMessage = parsedData.message;
          }
        } catch (e) {
          // Not JSON, use raw data
        }

        // Call the message callback
        if (callbacks.onMessage) {
          callbacks.onMessage(statusMessage);
        }

        // Check if processing is complete
        if (statusMessage === 'DONE' || statusMessage === 'COMPLETED' || statusMessage === 'Failed') {
          console.log('[RequestService] Received final status. Closing EventSource.');
          eventSource.close();
          if (callbacks.onCompleted) {
            callbacks.onCompleted();
          }
        }
      } catch (error) {
        console.error('[RequestService] Error processing message data:', error);
        if (callbacks.onError) {
          callbacks.onError('Error processing status update.');
        }
      }
    };

    // Register the error event handler
    eventSource.onerror = (error) => {
      console.error('[RequestService] EventSource error:', error);
      if (eventSource.readyState !== EventSource.CLOSED) {
        if (callbacks.onError) {
          callbacks.onError('Connection error with status stream.');
        }
      }
      eventSource.close();
    };

    // Return a cleanup function
    return () => {
      console.log('[RequestService] Cleaning up EventSource.');
      eventSource.close();
    };
  }
};

export default requestService;