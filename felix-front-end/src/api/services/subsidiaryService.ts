import api from '../axiosConfig';

export interface Subsidiary {
  id?: string;
  tenant_id: string;
  email: string;
  contact_name: string;
  entity_name: string;
  entity_code: string;
  created_at?: string;
  updated_at?: string;
}

export interface SubsidiaryCreate {
  tenant_id: string;
  email: string;
  contact_name: string;
  entity_name: string;
  entity_code: string;
}

export interface SubsidiaryUpdate {
  tenant_id: string;
  email: string;
  contact_name: string;
  entity_name: string;
  entity_code: string;
}

const subsidiaryService = {
  /**
   * Get all subsidiaries (paginated)
   */
  async getSubsidiaries(params?: { limit?: number; order?: string; after?: string; before?: string }): Promise<any> {
    try {
      const response = await api.get('/api/v1/subsidiary', { params });
      console.log('Fetched subsidiaries:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching subsidiaries:', error);
      throw error;
    }
  },

  /**
   * Get a single subsidiary by ID
   */
  async getSubsidiary(subsidiaryId: string): Promise<any> {
    try {
      const response = await api.get(`/api/v1/subsidiary/${subsidiaryId}`);
      console.log('Fetched subsidiary:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching subsidiary:', error);
      throw error;
    }
  },

  /**
   * Create a new subsidiary
   */
  async createSubsidiary(data: SubsidiaryCreate): Promise<any> {
    try {
      const response = await api.post('/api/v1/subsidiary', data);
      console.log('Created subsidiary:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating subsidiary:', error);
      throw error;
    }
  },

  /**
   * Update an existing subsidiary
   */
  async updateSubsidiary(subsidiaryId: string, data: SubsidiaryUpdate): Promise<any> {
    try {
      const response = await api.put(`/api/v1/subsidiary/${subsidiaryId}`, data);
      console.log('Updated subsidiary:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating subsidiary:', error);
      throw error;
    }
  },

  /**
   * Delete a subsidiary by ID
   */
  async deleteSubsidiary(subsidiaryId: string): Promise<any> {
    try {
      const response = await api.delete(`/api/v1/subsidiary/${subsidiaryId}`);
      console.log('Deleted subsidiary:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error deleting subsidiary:', error);
      throw error;
    }
  },
};

export default subsidiaryService; 