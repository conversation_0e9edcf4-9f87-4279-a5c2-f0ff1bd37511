import api from '../axiosConfig';

export interface ProcessRequest {
  request_id: string;
}

/**
 * Service for handling Felix AI processing operations
 */
const processService = {
  /**
   * Initiates the processing of a request
   * @param requestId - The ID of the request to process
   * @returns The response from the server
   */
  async processRequest(requestId: string): Promise<any> {
    try {
      console.log(`Initiating processing for request ${requestId}`);

      const requestData: ProcessRequest = {
        request_id: requestId
      };

      // The axiosConfig automatically adds the Authorization header with the token
      const response = await api.post('/api/v1/felix-ai-api/process', requestData);

      console.log('Process request response:', response.data);
      return response.data;
    } catch (error: any) {
      // Detailed error handling for better debugging
      let errorMessage = 'Error initiating processing';

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        errorMessage = `Server error (${status}): ${message}`;

        // Add specific handling for common errors
        if (status === 401) {
          errorMessage = 'Authentication failed. Please sign in again.';
          console.error('Authentication error when processing request:', error.response.data);
        } else if (status === 400) {
          errorMessage = `Bad request: ${message}`;
          console.error('Bad request error:', error.response.data);
        } else if (status === 404) {
          errorMessage = `Request not found: ${message}`;
          console.error('Resource not found:', error.response.data);
        }
      } else if (error.request) {
        // Request made but no response received
        errorMessage = 'No response received from server. Please check your connection and try again.';
      }

      console.error('Error processing request:', errorMessage, error);
      throw new Error(errorMessage);
    }
  },

  /**
   * Gets the results of a processed request
   * @param requestId - The ID of the request to get results for
   * @returns The response from the server containing the results
   */
  async getResults(requestId: string): Promise<any> {
    try {
      console.log(`Getting results for request ${requestId}`);

      // The axiosConfig automatically adds the Authorization header with the token
      const response = await api.get(`/api/v1/felix-ai-api/process/${requestId}`);

      console.log('Get results response:', response.data);
      return response.data;
    } catch (error: any) {
      // Detailed error handling for better debugging
      let errorMessage = 'Error getting results';

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const message = error.response.data?.message || error.message;

        errorMessage = `Server error (${status}): ${message}`;

        // Add specific handling for common errors
        if (status === 401) {
          errorMessage = 'Authentication failed. Please sign in again.';
          console.error('Authentication error when getting results:', error.response.data);
        } else if (status === 400) {
          errorMessage = `Bad request: ${message}`;
          console.error('Bad request error:', error.response.data);
        } else if (status === 404) {
          errorMessage = `Results not found: ${message}`;
          console.error('Results not found:', error.response.data);
        }
      } else if (error.request) {
        // Request made but no response received
        errorMessage = 'No response received from server. Please check your connection and try again.';
      }

      console.error('Error getting results:', errorMessage, error);
      throw new Error(errorMessage);
    }
  }
};

export default processService;