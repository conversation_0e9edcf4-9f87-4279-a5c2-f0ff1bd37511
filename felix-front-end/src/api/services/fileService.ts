import api from '../axiosConfig';

/**
 * Interface for file upload request that matches the API requirements
 */
export interface FileUploadRequest {
  base64_data: string;
  file_name: string;
  file_type: 'pdf' | 'csv';
  file_id: string;  // This is required by the backend
}

/**
 * Interface for additional file metadata
 */
export interface FileMetadata {
  fileId: string;     // Required - needed for the upload API
  requestId?: string;
  fileName?: string;
  filePath?: string;
}

// Maximum file size in bytes (10MB)
// const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Service for handling file operations
 */
const fileService = {
  /**
   * Uploads a file to the server
   * @param file - The file to upload
   * @param fileType - The type of file (pdf or csv)
   * @param metadata - Required metadata about the file (must include fileId)
   * @returns The response from the server
   */
  async uploadFile(file: File, fileType: 'pdf' | 'csv', metadata: FileMetadata): Promise<any> {
    try {
      // Check file size before conversion and upload
    //   if (file.size > MAX_FILE_SIZE) {
    //     throw new Error(`File size exceeds maximum allowed (${MAX_FILE_SIZE / (1024 * 1024)}MB)`);
    //   }

      // Log the upload details
      console.log(`Starting upload for ${file.name} (${file.size} bytes) with file ID: ${metadata.fileId}`);

      // Convert file to base64
      const base64Data = await fileToBase64(file);

      // Ensure we have a file_id - it's required by the backend
      if (!metadata?.fileId) {
        throw new Error('File ID is required for upload. Please create a file record first.');
      }

      // Create the request data with the required file_id
      // Make sure the file_type is exactly 'pdf' or 'csv' as required by the backend enum
      const normalizedFileType = fileType.toLowerCase() === 'pdf' ? 'pdf' : 'csv';

      const requestData: FileUploadRequest = {
        base64_data: base64Data,
        file_name: file.name,
        file_type: normalizedFileType as 'pdf' | 'csv',
        file_id: metadata.fileId
      };

      // Use the dedicated upload endpoint
      const endpoint = '/api/v1/felix-ai-api/upload';

      // Log the request data for debugging (without the base64 data for brevity)
      console.log('File upload request data:', {
        file_name: requestData.file_name,
        file_type: requestData.file_type,
        file_id: requestData.file_id,
        base64_data_length: requestData.base64_data.length
      });

      try {
        const response = await api.post(endpoint, requestData);
        console.log('Upload successful:', file.name, `(File ID: ${metadata.fileId})`);
        console.log('Upload response:', response.data);

        // Check if the response has the expected format
        // The structure should be { message_code: 0, message: 'File uploaded successfully', data: null }
        if (response.data && typeof response.data.message_code === 'number') {
          if (response.data.message_code !== 0) {
            // If message_code is not 0, it indicates an error
            throw new Error(`Upload failed: ${response.data.message || 'Unknown error'}`);
          }
        }

        return response;
      } catch (error: any) {
        console.error('Upload API error details:', error.response?.data);
        console.error('Upload API error status:', error.response?.status);
        throw error;
      }
    } catch (error: any) {
      // Provide detailed error information
      let errorMessage = 'Error uploading file';

      if (error.response) {
        // Server responded with error
        errorMessage = `Server error (${error.response.status}): ${error.response.data?.message || error.message}`;
        console.error('Upload error response:', error.response.data);

        // Log more details for debugging
        if (error.response.data?.errors) {
          console.error('Error details:', error.response.data.errors);
        }
      } else if (error.request) {
        // Request made but no response
        errorMessage = `No response from server: ${error.message}`;
      } else {
        // Error setting up request
        errorMessage = `Upload failed: ${error.message}`;
      }

      console.error('Error uploading file:', errorMessage);
      throw new Error(errorMessage);
    }
  },

  /**
   * Creates a file record in the database
   * @param requestId - The ID of the request
   * @param fileName - The name of the file
   * @param filePath - The path to the file
   * @returns The response from the server
   */
  async createFileRecord(requestId: string, fileName: string, filePath: string): Promise<any> {
    try {
      console.log(`Creating file record for ${fileName} (requestId: ${requestId})`);
      const response = await api.post('/api/v1/file', {
        request_id: requestId,
        file_name: fileName,
        file_path: filePath,
        blob_url: ''
      });

      // Log the response for debugging
      console.log('File record created successfully:', response.data);

      // Return the entire response, not just the data
      // This ensures we maintain the expected response structure
      return response;
    } catch (error: any) {
      let errorMessage = 'Error creating file record';

      if (error.response) {
        errorMessage = `Server error (${error.response.status}): ${error.response.data?.message || error.message}`;
      } else {
        errorMessage = `Request failed: ${error.message}`;
      }

      console.error('Error creating file record:', errorMessage);
      throw new Error(errorMessage);
    }
  }
};

/**
 * Converts a file to a base64 string
 * @param file - The file to convert
 * @returns A promise that resolves to a base64 string
 */
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      try {
        let base64 = reader.result as string;
        // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
        base64 = base64.split(',')[1];
        resolve(base64);
      } catch (error) {
        reject(new Error(`Failed to process file: ${error}`));
      }
    };
    reader.onerror = error => {
      reject(new Error(`Failed to read file: ${error}`));
    };
  });
}

export default fileService;