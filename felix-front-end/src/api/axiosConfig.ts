import axios from 'axios';
import config from '../config';
import { getAccessToken } from '../auth/authService';

// Create an Axios instance with custom config
const api = axios.create({
  baseURL: config.api.baseUrl || 'http://135.116.8.117:8080',
  timeout: config.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Function to get the auth token from various sources
const getAuthToken = async (): Promise<string | null> => {
  try {
    // Try to get a fresh access token with the correct scope
    const accessToken = await getAccessToken();
    return accessToken;
  } catch (error) {
    console.error('Error getting access token:', error);

    // Fallback to stored token if available
    const token = localStorage.getItem(config.api.auth.tokenKey);
    if (token) return token;

    return null;
  }
};

// getAccessToken is imported at the top of the file

// Request interceptor for adding auth token
api.interceptors.request.use(
  async (config) => {
    try {
      // Get token from appropriate source
      const token = await getAuthToken();

      // If token exists, add it to headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error adding auth token to request:', error);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle token expiration and refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to get a fresh token
        console.warn('Authentication failed (401), trying to get a fresh token');
        const token = await getAccessToken();

        if (token) {
          // Update the Authorization header with the new token
          originalRequest.headers.Authorization = `Bearer ${token}`;
          console.log('Retrying request with fresh token');
          return axios(originalRequest);
        }
      } catch (refreshError) {
        console.error('Failed to refresh token:', refreshError);
        // For production, redirect to login page for re-authentication
        console.warn('Authentication failed, redirecting to login');
        // window.location.href = '/login';
      }
    }

    // Log detailed error information
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error:', error.message);
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Request Error:', error.message);
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }

    return Promise.reject(error);
  }
);

export default api;