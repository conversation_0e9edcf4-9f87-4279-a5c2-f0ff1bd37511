import { useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import useRouteRestore from './hooks/useRouteRestore';

// Layouts
import LandingLayout from './layouts/LandingLayout';
import DashboardLayout from './layouts/DashboardLayout';

// Pages
import LandingPage from './pages/LandingPage';
import NotFoundPage from './pages/NotFoundPage';
import Dashboard from './pages/dashboard/Dashboard';
import ActiveProcesses from './pages/dashboard/ActiveProcesses';
import History from './pages/dashboard/History';
import Settings from './pages/dashboard/Settings';
import OAuth2Redirect from './pages/auth/OAuth2Redirect';
import UseCaseRedirect from './pages/dashboard/UseCaseRedirect';

// Use Case Pages
import AnalyticalReview from './pages/dashboard/use-cases/AnalyticalReview';
import ExchangeRateReview from './pages/dashboard/use-cases/ExchangeRateReview';
import IntercompanyReconciliation from './pages/dashboard/use-cases/IntercompanyReconciliation';
import RoundingIssueResolution from './pages/dashboard/use-cases/RoundingIssueResolution';

// Auth
import AuthGuard from './components/auth/AuthGuard';

// Syncfusion
import { registerLicense } from '@syncfusion/ej2-base';

function App() {
  registerLicense("Ngo9BigBOggjHTQxAR8/V1NNaF1cWWhPYVF3WmFZfVtgcl9EZFZURmYuP1ZhSXxWdkBhW39YcnBQQGVUV0Z9XUs=");

  const location = useLocation();
  
  // Use route restore hook to maintain page state on refresh
  useRouteRestore();

  // Scroll to top on route change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/" element={<LandingLayout />}>
        <Route index element={<LandingPage />} />
      </Route>

      {/* Auth routes */}
      <Route path="/oauth2-redirect" element={<OAuth2Redirect />} />

      {/* Protected routes */}
      <Route
        path="/dashboard"
        element={
          <AuthGuard>
            <DashboardLayout />
          </AuthGuard>
        }
      >
        <Route index element={<Dashboard />} />
        <Route path="active-processes" element={<ActiveProcesses />} />
        <Route path="history" element={<History />} />
        <Route path="settings" element={<Settings />} />

        {/* Use Case Routes */}
        <Route path="use-case/analytical-review/*" element={<AnalyticalReview />} />
        <Route path="use-case/exchange-rate-review/*" element={<ExchangeRateReview />} />
        <Route path="use-case/intercompany-reconciliation/*" element={<IntercompanyReconciliation />} />
        <Route path="use-case/rounding-issue-resolution/*" element={<RoundingIssueResolution />} />

        {/* Redirect from old URL format */}
        <Route path="use-case/:id/*" element={<UseCaseRedirect />} />
      </Route>

      {/* 404 route */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
}

export default App;