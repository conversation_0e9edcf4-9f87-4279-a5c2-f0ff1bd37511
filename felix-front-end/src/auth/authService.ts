import { msalInstance } from './MsalProvider';
import { loginRequest, msalConfig, tokenScope } from './msalConfig';
import { AccountInfo, AuthenticationResult, InteractionRequiredAuthError, SilentRequest, BrowserAuthError } from '@azure/msal-browser';
import config from '../config';

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: string;
}

// Helper function to ensure MSAL is initialized before using it
const ensureMsalInitialized = async () => {
  try {
    // Check if MSAL is initialized by trying to get accounts
    // This will throw if not initialized
    msalInstance.getAllAccounts();
    return true;
  } catch (error) {
    if (error instanceof BrowserAuthError &&
        error.errorCode === 'uninitialized_public_client_application') {
      console.log('MSAL not initialized, initializing now...');
      await msalInstance.initialize();
      return true;
    }
    throw error;
  }
};

/**
 * Save authentication data in localStorage for API access
 * @param response Authentication result from MSAL
 */
const saveAuthDataToLocalStorage = (response: AuthenticationResult) => {
  try {
    // Save the full account object in localStorage for easier access by API services
    localStorage.setItem('msal.account', JSON.stringify(response.account));

    // Save the tokens separately for easier access
    // The access token is the one we need for API calls
    localStorage.setItem(config.api.auth.tokenKey, response.accessToken);
    localStorage.setItem('msal.idtoken', response.idToken);

    // Save the user profile in the expected location
    const userProfile = processAuthResult(response);
    localStorage.setItem(config.api.auth.userKey, JSON.stringify(userProfile));

    console.log('Auth data saved to localStorage', {
      hasAccount: !!localStorage.getItem('msal.account'),
      hasToken: !!localStorage.getItem(config.api.auth.tokenKey),
      hasUserProfile: !!localStorage.getItem(config.api.auth.userKey),
      tokenExpiresOn: response.expiresOn
    });

    // Log token information for debugging
    console.log('Token scopes:', response.scopes);
    console.log('Token expires on:', response.expiresOn);
  } catch (error) {
    console.error('Error saving auth data to localStorage:', error);
  }
};

export const login = async (): Promise<UserProfile> => {
  try {
    // Ensure MSAL is initialized
    await ensureMsalInitialized();

    // Check if we have an active account
    const activeAccount = msalInstance.getActiveAccount();

    if (activeAccount) {
      // If we have an active account, try silent login first
      try {
        const silentRequest: SilentRequest = {
          scopes: loginRequest.scopes,
          account: activeAccount
        };

        const response = await msalInstance.acquireTokenSilent(silentRequest);
        // Save auth data for API access
        saveAuthDataToLocalStorage(response);
        return processAuthResult(response);
      } catch (error) {
        // If silent login fails, fall through to interactive login
        console.log("Silent token acquisition failed, falling back to interactive login", error);
      }
    }

    // No active account or silent login failed, use popup login
    // Make sure to include the specific scope required by the Felix-AI backend
    const customLoginRequest = {
      ...loginRequest,
      scopes: [
        ...loginRequest.scopes,
        tokenScope
      ]
    };

    const response = await msalInstance.loginPopup(customLoginRequest);
    msalInstance.setActiveAccount(response.account);

    // Save auth data for API access
    saveAuthDataToLocalStorage(response);

    return processAuthResult(response);
  } catch (error) {
    console.error('Login failed:', error);
    throw new Error('Login failed. Please try again.');
  }
};

export const logout = async (): Promise<void> => {
  try {
    // Ensure MSAL is initialized
    await ensureMsalInitialized();

    // Get the active account
    const account = msalInstance.getActiveAccount();

    // If there's an active account, log out with popup
    if (account) {
      await msalInstance.logoutPopup({
        account,
        postLogoutRedirectUri: window.location.origin,
        mainWindowRedirectUri: window.location.origin
      });
    }

    // Clear all accounts from the cache
    const accounts = msalInstance.getAllAccounts();
    if (accounts.length > 0) {
      // Clear browser storage
      sessionStorage.clear();
      localStorage.removeItem(`msal.${msalConfig.auth.clientId}.idtoken`);

      // Clear our custom storage items
      localStorage.removeItem('msal.account');
      localStorage.removeItem('msal.idtoken');
      localStorage.removeItem(config.api.auth.tokenKey);
      localStorage.removeItem(config.api.auth.userKey);

      // For each account, attempt to remove it
      accounts.forEach(acct => {
        try {
          msalInstance.setActiveAccount(acct);
        } catch (e) {
          console.log("Error setting active account during logout cleanup", e);
        }
      });
    }

    // Set active account to null
    msalInstance.setActiveAccount(null);

    console.log("Logout successful");
  } catch (error) {
    console.error("Logout error:", error);
    // Even if there's an error, we want to clear the local state
    try {
      msalInstance.setActiveAccount(null);
    } catch (e) {
      console.error("Error clearing active account during logout error handling", e);
    }
    throw error;
  }
};

export const getAccessToken = async (): Promise<string> => {
  try {
    // Ensure MSAL is initialized
    await ensureMsalInitialized();

    // Get all accounts
    const accounts = msalInstance.getAllAccounts();

    // If no active account is set but accounts exist in the cache, set the first one as active
    if (!msalInstance.getActiveAccount() && accounts.length > 0) {
      msalInstance.setActiveAccount(accounts[0]);
    }

    const account = msalInstance.getActiveAccount();
    if (!account) {
      // No account found, need to login first
      const authResult = await msalInstance.loginPopup(loginRequest);
      msalInstance.setActiveAccount(authResult.account);

      // Save auth data for API access
      saveAuthDataToLocalStorage(authResult);

      console.log('Access token:', authResult.accessToken);

      return authResult.accessToken;
    }

    // Define the scopes for the backend API
    // Use the token scope imported from msalConfig
    const tokenScopes = [
      tokenScope
    ];

    // Try to acquire token silently
    try {
      const silentRequest: SilentRequest = {
        scopes: tokenScopes,
        account
      };

      const response = await msalInstance.acquireTokenSilent(silentRequest);

      // Save auth data for API access
      saveAuthDataToLocalStorage(response);

      console.log('Access token:', response.accessToken);

      return response.accessToken;
    } catch (error) {
      // If silent acquisition fails, fall back to popup
      if (error instanceof InteractionRequiredAuthError) {
        const response = await msalInstance.acquireTokenPopup({
          scopes: tokenScopes,
          account
        });

        // Save auth data for API access
        saveAuthDataToLocalStorage(response);

        return response.accessToken;
      }
      throw error;
    }
  } catch (error) {
    console.error('Failed to get access token:', error);
    throw new Error('Authentication failed. Please sign in again.');
  }
};

export const getActiveAccount = async (): Promise<AccountInfo | null> => {
  try {
    // Ensure MSAL is initialized
    await ensureMsalInitialized();
    return msalInstance.getActiveAccount();
  } catch (error) {
    console.error('Error getting active account:', error);
    return null;
  }
};

/**
 * Gets the user ID from the active account or localStorage
 * @returns The user ID as string
 */
export const getUserId = (): string => {
  try {
    // First, try getting it from localStorage
    const userProfile = localStorage.getItem(config.api.auth.userKey);
    if (userProfile) {
      const user = JSON.parse(userProfile);
      if (user.id) return user.id;
    }

    // Then try the MSAL account
    const msalAccountStr = localStorage.getItem('msal.account');
    if (msalAccountStr) {
      const msalAccount = JSON.parse(msalAccountStr);
      if (msalAccount.idTokenClaims?.oid) return msalAccount.idTokenClaims.oid;
      if (msalAccount.localAccountId) return msalAccount.localAccountId;
    }

    // Check active account from MSAL instance directly
    const activeAccount = msalInstance.getActiveAccount();
    if (activeAccount) {
      if (activeAccount.idTokenClaims?.oid) return activeAccount.idTokenClaims.oid as string;
      return activeAccount.localAccountId;
    }

    throw new Error('No user ID found');
  } catch (error) {
    console.error('Error getting user ID:', error);
    return '';
  }
};

// Helper function to process authentication result
const processAuthResult = (response: AuthenticationResult): UserProfile => {
  const account = response.account;
  const claims = account.idTokenClaims || {};

  // Create user profile from account info
  return {
    id: claims.oid as string || account.localAccountId,
    name: account.name || 'Unknown User',
    email: account.username,
    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(account.name || 'User')}&background=1A3A4A&color=fff`,
    role: 'User' // Default role, can be updated based on claims or backend response
  };
};
