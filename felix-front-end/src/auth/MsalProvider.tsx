import { ReactNode, useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>rovider as MsalReactProvider } from '@azure/msal-react';
import {
  PublicClientApplication,
  EventType,
  EventMessage,
  AuthenticationResult,
  BrowserAuthError
} from '@azure/msal-browser';
import { msalConfig } from './msalConfig';

// Create MSAL instance but don't export it yet
const msalInstance = new PublicClientApplication(msalConfig);

// Initialize MSAL instance
let msalInitialized = false;
const initializeMsal = async () => {
  if (!msalInitialized) {
    try {
      await msalInstance.initialize();
      msalInitialized = true;
      console.log('MSAL initialized successfully');

      // Set up event callbacks after initialization
      msalInstance.addEventCallback((event: EventMessage) => {
        // Set active account after login success
        if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
          const payload = event.payload as AuthenticationResult;
          const account = payload.account;
          msalInstance.setActiveAccount(account);
          console.log('Login successful, active account set:', account);
        }

        // Handle account selection
        if (event.eventType === EventType.ACCOUNT_ADDED ||
            event.eventType === EventType.ACCOUNT_REMOVED) {
          try {
            const accounts = msalInstance.getAllAccounts();
            if (accounts.length > 0) {
              // Set the first account as active if there's no active account
              if (!msalInstance.getActiveAccount()) {
                msalInstance.setActiveAccount(accounts[0]);
                console.log('Active account set after account change:', accounts[0]);
              }
            }
          } catch (error) {
            console.error('Error handling account change:', error);
          }
        }
      });

      // Initialize active account on startup
      try {
        const accounts = msalInstance.getAllAccounts();
        if (accounts.length > 0 && !msalInstance.getActiveAccount()) {
          msalInstance.setActiveAccount(accounts[0]);
          console.log('Initial active account set:', accounts[0]);
        }
      } catch (error) {
        console.error('Error setting initial active account:', error);
      }
    } catch (error) {
      console.error('Failed to initialize MSAL:', error);
      throw error;
    }
  }
  return msalInstance;
};

// Initialize immediately
initializeMsal().catch(error => {
  console.error('Failed to initialize MSAL on startup:', error);
});

interface Props {
  children: ReactNode;
}

export const MsalProvider = ({ children }: Props) => {
  const [isInitialized, setIsInitialized] = useState(msalInitialized);
  const [isLoading, setIsLoading] = useState(!msalInitialized);

  // Initialize MSAL on component mount if not already initialized
  useEffect(() => {
    if (!isInitialized) {
      setIsLoading(true);
      initializeMsal()
        .then(() => {
          setIsInitialized(true);
          setIsLoading(false);
          console.log('MSAL Provider initialization complete');
        })
        .catch(error => {
          console.error('Failed to initialize MSAL in provider:', error);
          setIsLoading(false);
        });
    } else {
      // If already initialized, try to set active account
      try {
        const accounts = msalInstance.getAllAccounts();
        if (accounts.length > 0 && !msalInstance.getActiveAccount()) {
          msalInstance.setActiveAccount(accounts[0]);
          console.log('Active account set in MsalProvider:', accounts[0]);
        }
        setIsLoading(false);
      } catch (error) {
        if (error instanceof BrowserAuthError) {
          console.error('Browser auth error in MsalProvider:', error);
        } else {
          console.error('Error in MsalProvider:', error);
        }
        setIsLoading(false);
      }
    }
  }, [isInitialized]);

  // Show loading state while initializing
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
    </div>;
  }

  return (
    <MsalReactProvider instance={msalInstance}>
      {children}
    </MsalReactProvider>
  );
};

// Export the initialized instance
export { msalInstance };
