import { Configuration, PopupRequest, LogLevel } from "@azure/msal-browser";
import { getEnv } from "../utils/runtimeEnv";

// MSAL configuration
export const msalConfig: Configuration = {
  auth: {
    clientId: getEnv('CLIENT_ID'),
    authority: `https://login.microsoftonline.com/${getEnv('TENANT_ID')}`,
    redirectUri: window.location.origin + getEnv('REDIRECT_URI'),
    navigateToLoginRequestUrl: true,
  },
  cache: {
    cacheLocation: "localStorage", // Changed to localStorage for better persistence
    storeAuthStateInCookie: true, // Set to true for better compatibility
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            break;
          case LogLevel.Info:
            console.info(message);
            break;
          case LogLevel.Verbose:
            console.debug(message);
            break;
          case LogLevel.Warning:
            console.warn(message);
            break;
        }
      },
      logLevel: LogLevel.Verbose,
    }
  }
};

// Get the token scope from runtime environment variables
export const tokenScope = getEnv('TOKEN_SCOPE');

// Add scopes here for ID token to be used at Microsoft identity platform endpoints.
export const loginRequest: PopupRequest = {
  // Request basic user profile scopes and the specific scope required by the Felix-AI backend
  scopes: [
    "openid",
    "profile",
    "email",
    "User.Read",
    tokenScope
  ],
  prompt: "select_account", // Force account selection to ensure proper login
  extraScopesToConsent: [tokenScope] // Explicitly request consent for the API scope
};

// Add the endpoints here for Microsoft Graph API services you'd like to use.
export const graphConfig = {
  graphMeEndpoint: "https://graph.microsoft.com/v1.0/me"
};
