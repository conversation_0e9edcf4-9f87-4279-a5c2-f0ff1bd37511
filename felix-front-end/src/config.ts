import { getEnv } from './utils/runtimeEnv';

/**
 * Application configuration with default values
 *
 * This configuration uses runtime environment variables when available,
 * falling back to build-time environment variables for local development.
 */
export const config = {
  /**
   * API configuration
   */
  api: {
    /**
     * Base URL for API calls
     */
    baseUrl: getEnv('BACKEND_URL', 'http://localhost:8000'),

    /**
     * Default timeout for API requests in milliseconds
     */
    timeout: 30000,

    /**
     * Authentication configuration
     */
    auth: {
      /**
       * Local storage key for auth token
       */
      tokenKey: 'auth_token',

      /**
       * Local storage key for user information
       */
      userKey: 'user',
    }
  },

  /**
   * Feature flags
   */
  features: {
    /**
     * Enable file upload functionality
     */
    fileUpload: true,

    /**
     * Enable real-time communication
     */
    realTimeCommunication: false,
  }
};

export default config;