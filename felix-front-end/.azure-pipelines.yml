name: $(Date:yyyyMMdd)$(Rev:.r)

trigger:
- develop

pool:
  name: 'azure ubuntu'

variables:
  acrName: 'crfelixdevswedencentralih0.azurecr.io'
  imageName: 'felix-ai-fe'
  shortCommitId: $[substring(variables['Build.SourceVersion'], 0, 8)]  
  tagDev: dev-$(shortCommitId)
  gitOpsRepo: 'https://<EMAIL>/felix-sre/Felix-AI/_git/felix-ai-gitops'  
  releaseFilePath: 'infrastructure/felix-ai-frontend/release.yaml'  
  patToken: $(MY_PAT)  

steps:
- task: Docker@2
  displayName: 'Login to ACR'
  inputs:
    command: 'login'
    containerRegistry: 'acr-service-connection'

- task: Docker@2
  displayName: 'Build Docker Image'
  inputs:
    command: 'build'
    repository: '$(imageName)'
    Dockerfile: 'docker/Dockerfile'
    buildContext: '.'
    tags: |
      latest
      $(tagDev)

- task: Docker@2
  displayName: 'Push Docker Image'
  inputs:
    command: 'push'
    repository: '$(imageName)'
    tags: |
      latest
      $(tagDev)

- script: |
    echo "Cloning GitOps Repository"
    git clone https://$(patToken)@dev.azure.com/felix-sre/Felix-AI/_git/felix-ai-gitops
    cd felix-ai-gitops  
    git checkout test
  displayName: 'Clone GitOps Repository'

- script: |
    echo "Updating tag in release.yaml"
    cd felix-ai-gitops 
    pwd
    ls -la
    sed -i 's/tag: ".*"/tag: "$(tagDev)"/g' $(releaseFilePath)
    cat $(releaseFilePath)
  displayName: 'Update tag in release.yaml'

- script: |
    cd felix-ai-gitops 
    git config --global user.email "<EMAIL>"
    git config --global user.name "Azure DevOps"
    git add $(releaseFilePath)
    git commit -m "Update release.yaml with new tag $(tagDev)"
    git push origin HEAD:test  
  displayName: 'Commit and Push Changes to GitOps Repo'
  env:
    MY_PAT: $(MY_PAT)  