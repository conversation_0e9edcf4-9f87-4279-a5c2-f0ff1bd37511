#!/bin/sh
set -e

# Script to generate runtime environment configuration for the frontend
# This script is executed by the nginx entrypoint system
# It creates a JavaScript file with environment variables that can be loaded at runtime

echo "🔧 Generating runtime environment configuration..."

# Output file path
OUTPUT_FILE=/usr/share/nginx/html/env-config.js
OUTPUT_DIR=$(dirname "$OUTPUT_FILE")

# Ensure the output directory exists
if [ ! -d "$OUTPUT_DIR" ]; then
    echo "❌ Error: Output directory $OUTPUT_DIR does not exist!"
    # Try to create it
    mkdir -p "$OUTPUT_DIR" || { echo "Failed to create $OUTPUT_DIR"; exit 1; }
fi

# Check if we have write permissions to the output directory
if [ ! -w "$OUTPUT_DIR" ]; then
    echo "❌ Error: No write permission to $OUTPUT_DIR!"
    # Try to fix permissions
    chmod 755 "$OUTPUT_DIR" || { echo "Failed to set permissions on $OUTPUT_DIR"; exit 1; }
fi

# Create env-config.js with window._env_ object
echo "Creating runtime environment configuration at $OUTPUT_FILE"

# Start writing to file
echo "window._env_ = {" > $OUTPUT_FILE

# Add environment variables with VITE_ prefix
# These will be accessible in the frontend application
echo "  BACKEND_URL: \"${VITE_BACKEND_URL:-''}\"," >> $OUTPUT_FILE
echo "  TENANT_ID: \"${VITE_TENANT_ID:-''}\"," >> $OUTPUT_FILE
echo "  CLIENT_ID: \"${VITE_CLIENT_ID:-''}\"," >> $OUTPUT_FILE
echo "  REDIRECT_URI: \"${VITE_REDIRECT_URI:-''}\"," >> $OUTPUT_FILE
echo "  TOKEN_SCOPE: \"${VITE_TOKEN_SCOPE:-''}\"," >> $OUTPUT_FILE

# Close the object
echo "};" >> $OUTPUT_FILE

# Make the file readable
chmod 755 $OUTPUT_FILE

# Verify the file was created successfully
if [ -f "$OUTPUT_FILE" ]; then
    echo "✅ Runtime environment configuration generated successfully"
    ls -la $OUTPUT_FILE
else
    echo "❌ Error: Failed to create $OUTPUT_FILE!"
    exit 1
fi
