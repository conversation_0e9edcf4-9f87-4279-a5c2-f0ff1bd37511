# Build stage
FROM node:20-alpine as build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the code
COPY . .

# Build the app
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Copy scripts
COPY docker/generate-env-config.sh /docker-entrypoint.d/40-generate-env-config.sh
COPY docker/entrypoint.sh /docker-entrypoint.d/50-custom-entrypoint.sh

# Make scripts executable
RUN chmod +x /docker-entrypoint.d/40-generate-env-config.sh \
    && chmod +x /docker-entrypoint.d/50-custom-entrypoint.sh

# Expose port
EXPOSE 80

# Use the default entrypoint from nginx image
# The scripts in /docker-entrypoint.d/ will be executed automatically
CMD ["nginx", "-g", "daemon off;"]
