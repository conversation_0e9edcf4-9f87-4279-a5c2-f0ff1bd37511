#!/bin/sh
set -e

# This script is executed by the nginx entrypoint system
# It runs after the generate-env-config.sh script

# Print environment variables for debugging (excluding sensitive information)
echo "Container environment configured with:"
echo "VITE_BACKEND_URL=${VITE_BACKEND_URL:-not set}"
echo "VITE_TENANT_ID=****" # Masked for security
echo "VITE_CLIENT_ID=****" # Masked for security
echo "VITE_REDIRECT_URI=${VITE_REDIRECT_URI:-not set}"
echo "VITE_TOKEN_SCOPE=****" # Masked for security

# Verify the env-config.js file was created
if [ -f /usr/share/nginx/html/env-config.js ]; then
    echo "✅ env-config.js was successfully created"
    # Print first line to verify (without sensitive info)
    head -n 1 /usr/share/nginx/html/env-config.js
else
    echo "❌ WARNING: env-config.js was not created!"
    # Try to create it again
    echo "Attempting to create env-config.js again..."
    /docker-entrypoint.d/40-generate-env-config.sh
fi

echo "✅ Frontend container is ready to serve content"
