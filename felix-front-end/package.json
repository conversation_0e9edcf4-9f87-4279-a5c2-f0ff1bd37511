{"name": "orinkia-sa", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^4.11.1", "@azure/msal-react": "^3.0.11", "@fontsource/inter": "^5.0.17", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "axios": "^1.9.0", "chart.js": "^4.4.1", "clsx": "^2.1.0", "framer-motion": "^11.0.6", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^6.22.1", "tailwind-merge": "^2.2.1", "uuid": "^11.1.0", "@syncfusion/ej2-base": "29.2.4", "@syncfusion/ej2-data": "29.2.4", "@syncfusion/ej2-lists": "29.2.4", "@syncfusion/ej2-navigations": "29.2.5", "@syncfusion/ej2-notifications": "29.2.4", "@syncfusion/ej2-popups": "29.2.4", "@syncfusion/ej2-dropdowns": "29.2.5", "@syncfusion/ej2-inputs": "29.2.5", "@syncfusion/ej2-splitbuttons": "29.2.4", "@syncfusion/ej2-buttons": "29.2.5", "@syncfusion/ej2-calendars": "29.2.5", "@syncfusion/ej2-excel-export": "29.2.4", "@syncfusion/ej2-pdf-export": "29.2.4", "@syncfusion/ej2-file-utils": "29.2.4", "@syncfusion/ej2-compression": "29.2.4", "@syncfusion/ej2-grids": "29.2.5", "@syncfusion/ej2-charts": "29.2.5", "@syncfusion/ej2-svg-base": "29.2.4", "@syncfusion/ej2-spreadsheet": "29.2.5", "@syncfusion/ej2-react-base": "29.2.4", "@syncfusion/ej2-react-spreadsheet": "29.2.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.14", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}